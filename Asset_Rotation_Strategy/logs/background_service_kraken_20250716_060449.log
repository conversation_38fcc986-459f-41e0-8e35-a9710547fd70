2025-07-16 06:04:49,195 - [KRAKE<PERSON>] - root - INFO - Exchange-specific logging initialized for kraken
2025-07-16 06:04:49,792 - [KRAKEN] - root - INFO - Telegram command handlers registered
2025-07-16 06:04:49,797 - [KRAKEN] - root - ERROR - Error in Telegram polling: set_wakeup_fd only works in main thread of the main interpreter
2025-07-16 06:04:49,798 - [KRAKEN] - root - INFO - Telegram bot polling started
2025-07-16 06:04:49,798 - [KRAKEN] - root - WARNING - Continuing without Telegram bot polling. Notifications will still be sent.
2025-07-16 06:04:49,798 - [KRAKEN] - root - INFO - Telegram notifier initialized with notification level: standard
2025-07-16 06:04:49,798 - [KRAKEN] - root - INFO - Telegram notification channel initialized
2025-07-16 06:04:49,799 - [KRAKEN] - root - INFO - Successfully loaded templates using utf-8 encoding
2025-07-16 06:04:49,799 - [KRAKEN] - root - INFO - <PERSON>aded 26 templates from file
2025-07-16 06:04:49,799 - [KRAKEN] - root - INFO - Notification manager initialized with 1 channels
2025-07-16 06:04:49,799 - [KRAKEN] - root - INFO - Notification manager initialized
2025-07-16 06:04:49,799 - [KRAKEN] - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-07-16 06:04:49,799 - [KRAKEN] - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-07-16 06:04:49,799 - [KRAKEN] - root - INFO - Set up critical time windows for 1d timeframe
2025-07-16 06:04:49,806 - [KRAKEN] - root - INFO - Network watchdog initialized with 10s check interval
2025-07-16 06:04:49,807 - [KRAKEN] - root - INFO - Loaded recovery state from data/state/recovery_state.json
2025-07-16 06:04:49,810 - [KRAKEN] - root - INFO - No state file found at /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/data/state/data/state/background_service_20250627_214259.json.json
2025-07-16 06:04:49,810 - [KRAKEN] - root - INFO - Recovery manager initialized
2025-07-16 06:04:49,810 - [KRAKEN] - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: kraken
2025-07-16 06:04:49,810 - [KRAKEN] - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'kraken', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'AAVE/EUR': 5.0, 'ADA/EUR': 2.0, 'AVAX/EUR': 4.0, 'BNB/EUR': 3.0, 'BTC/EUR': 3.5, 'DOGE/EUR': 5.0, 'DOT/EUR': 4.5, 'ETH/EUR': 7.5, 'LINK/EUR': 3.5, 'PEPE/EUR': 5.0, 'SOL/EUR': 4.5, 'SUI/EUR': 2.0, 'TRX/EUR': 3.0, 'XRP/EUR': 5.0, 'default': 5.0}, 'mode': 'live', 'order_type': 'market', 'position_size_pct': 99.99, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 20}, 'transaction_fee_rate': 0.004}
2025-07-16 06:04:49,810 - [KRAKEN] - root - INFO - Loading configuration from /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/config/settings.yaml...
2025-07-16 06:04:49,837 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:04:49,838 - [KRAKEN] - root - INFO - Getting credentials for exchange_id: kraken
2025-07-16 06:04:49,838 - [KRAKEN] - root - INFO - Looking for environment variables: KRAKEN_API_KEY, KRAKEN_API_SECRET
2025-07-16 06:04:49,838 - [KRAKEN] - root - INFO - Loaded API key from environment variable KRAKEN_API_KEY
2025-07-16 06:04:49,838 - [KRAKEN] - root - INFO - Loaded API secret from environment variable KRAKEN_API_SECRET
2025-07-16 06:04:49,838 - [KRAKEN] - root - INFO - Getting credentials for exchange_id: kraken
2025-07-16 06:04:49,838 - [KRAKEN] - root - INFO - Looking for environment variables: KRAKEN_API_KEY, KRAKEN_API_SECRET
2025-07-16 06:04:49,838 - [KRAKEN] - root - INFO - Loaded API key from environment variable KRAKEN_API_KEY
2025-07-16 06:04:49,838 - [KRAKEN] - root - INFO - Loaded API secret from environment variable KRAKEN_API_SECRET
2025-07-16 06:04:49,838 - [KRAKEN] - root - INFO - Loading configuration from /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/config/settings.yaml...
2025-07-16 06:04:49,876 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:04:51,804 - [KRAKEN] - root - INFO - Successfully loaded markets for kraken.
2025-07-16 06:04:53,964 - [KRAKEN] - root - INFO - Successfully connected to kraken exchange.
2025-07-16 06:04:53,964 - [KRAKEN] - root - INFO - Getting credentials for exchange_id: kraken
2025-07-16 06:04:53,964 - [KRAKEN] - root - INFO - Looking for environment variables: KRAKEN_API_KEY, KRAKEN_API_SECRET
2025-07-16 06:04:53,964 - [KRAKEN] - root - INFO - Loaded API key from environment variable KRAKEN_API_KEY
2025-07-16 06:04:53,964 - [KRAKEN] - root - INFO - Loaded API secret from environment variable KRAKEN_API_SECRET
2025-07-16 06:04:53,964 - [KRAKEN] - root - INFO - Loading configuration from /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/config/settings.yaml...
2025-07-16 06:04:54,014 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:04:58,097 - [KRAKEN] - root - INFO - Successfully connected to kraken exchange.
2025-07-16 06:04:58,101 - [KRAKEN] - root - INFO - Loading configuration from /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/config/settings.yaml...
2025-07-16 06:04:58,127 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:04:58,129 - [KRAKEN] - root - INFO - Trading executor initialized for kraken
2025-07-16 06:04:58,129 - [KRAKEN] - root - INFO - Trading mode: live
2025-07-16 06:04:58,129 - [KRAKEN] - root - INFO - Trading enabled: True
2025-07-16 06:04:58,129 - [KRAKEN] - root - INFO - Getting credentials for exchange_id: kraken
2025-07-16 06:04:58,130 - [KRAKEN] - root - INFO - Looking for environment variables: KRAKEN_API_KEY, KRAKEN_API_SECRET
2025-07-16 06:04:58,130 - [KRAKEN] - root - INFO - Loaded API key from environment variable KRAKEN_API_KEY
2025-07-16 06:04:58,130 - [KRAKEN] - root - INFO - Loaded API secret from environment variable KRAKEN_API_SECRET
2025-07-16 06:04:58,130 - [KRAKEN] - root - INFO - Loading configuration from /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/config/settings.yaml...
2025-07-16 06:04:58,156 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:04:59,977 - [KRAKEN] - root - INFO - Successfully loaded markets for kraken.
2025-07-16 06:05:02,240 - [KRAKEN] - root - INFO - Successfully connected to kraken exchange.
2025-07-16 06:05:02,241 - [KRAKEN] - root - INFO - Notification manager passed to trading executor
2025-07-16 06:05:02,241 - [KRAKEN] - root - INFO - Trading enabled in live mode
2025-07-16 06:05:05,254 - [KRAKEN] - root - INFO - Connected to kraken, balance: 131.2308 EUR
2025-07-16 06:05:05,255 - [KRAKEN] - root - INFO - Generated run ID: 20250716_060505
2025-07-16 06:05:05,255 - [KRAKEN] - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-07-16 06:05:05,255 - [KRAKEN] - root - INFO - Background service initialized
2025-07-16 06:05:05,259 - [KRAKEN] - root - INFO - Network watchdog started
2025-07-16 06:05:05,260 - [KRAKEN] - root - INFO - Network watchdog started
2025-07-16 06:05:05,264 - [KRAKEN] - root - INFO - Schedule set up for 1d timeframe
2025-07-16 06:05:05,271 - [KRAKEN] - root - INFO - Executing strategy (run #1)...
2025-07-16 06:05:05,272 - [KRAKEN] - root - INFO - Resetting daily trade counters for this strategy execution
2025-07-16 06:05:05,272 - [KRAKEN] - root - INFO - No trades recorded today (Max: 5)
2025-07-16 06:05:05,272 - [KRAKEN] - root - INFO - Initialized daily trades counter for 2025-07-16
2025-07-16 06:05:05,272 - [KRAKEN] - root - INFO - Creating snapshot for candle timestamp: 20250716
2025-07-16 06:05:05,287 - [KRAKEN] - root - INFO - Background service started
2025-07-16 06:05:05,413 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7000566483:AAEJRUb8vgIBBiC75kBGVhiRMEkoSQ2yRig/sendMessage "HTTP/1.1 200 OK"
2025-07-16 06:05:05,417 - [KRAKEN] - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-07-16 06:05:05,421 - [KRAKEN] - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-07-16 06:05:05,421 - [KRAKEN] - root - INFO - Using recent date for performance tracking: 2025-07-09
2025-07-16 06:05:05,422 - [KRAKEN] - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-07-16 06:05:05,531 - [KRAKEN] - root - INFO - Loaded 2157 rows of ETH/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:05,533 - [KRAKEN] - root - INFO - Last timestamp in cache for ETH/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,535 - [KRAKEN] - root - INFO - Expected last timestamp for ETH/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,537 - [KRAKEN] - root - INFO - Data is up to date for ETH/USDT
2025-07-16 06:05:05,539 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:05,580 - [KRAKEN] - root - INFO - Loaded 2157 rows of BTC/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:05,581 - [KRAKEN] - root - INFO - Last timestamp in cache for BTC/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,581 - [KRAKEN] - root - INFO - Expected last timestamp for BTC/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,584 - [KRAKEN] - root - INFO - Data is up to date for BTC/USDT
2025-07-16 06:05:05,588 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:05,616 - [KRAKEN] - root - INFO - Loaded 1800 rows of SOL/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:05,617 - [KRAKEN] - root - INFO - Last timestamp in cache for SOL/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,619 - [KRAKEN] - root - INFO - Expected last timestamp for SOL/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,619 - [KRAKEN] - root - INFO - Data is up to date for SOL/USDT
2025-07-16 06:05:05,620 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:05,637 - [KRAKEN] - root - INFO - Loaded 805 rows of SUI/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:05,639 - [KRAKEN] - root - INFO - Last timestamp in cache for SUI/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,640 - [KRAKEN] - root - INFO - Expected last timestamp for SUI/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,640 - [KRAKEN] - root - INFO - Data is up to date for SUI/USDT
2025-07-16 06:05:05,640 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:05,676 - [KRAKEN] - root - INFO - Loaded 2157 rows of XRP/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:05,676 - [KRAKEN] - root - INFO - Last timestamp in cache for XRP/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,677 - [KRAKEN] - root - INFO - Expected last timestamp for XRP/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,677 - [KRAKEN] - root - INFO - Data is up to date for XRP/USDT
2025-07-16 06:05:05,680 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:05,715 - [KRAKEN] - root - INFO - Loaded 1735 rows of AAVE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:05,716 - [KRAKEN] - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,718 - [KRAKEN] - root - INFO - Expected last timestamp for AAVE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,719 - [KRAKEN] - root - INFO - Data is up to date for AAVE/USDT
2025-07-16 06:05:05,720 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:05,748 - [KRAKEN] - root - INFO - Loaded 1758 rows of AVAX/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:05,749 - [KRAKEN] - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,749 - [KRAKEN] - root - INFO - Expected last timestamp for AVAX/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,750 - [KRAKEN] - root - INFO - Data is up to date for AVAX/USDT
2025-07-16 06:05:05,751 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:05,788 - [KRAKEN] - root - INFO - Loaded 2157 rows of ADA/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:05,788 - [KRAKEN] - root - INFO - Last timestamp in cache for ADA/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,791 - [KRAKEN] - root - INFO - Expected last timestamp for ADA/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,791 - [KRAKEN] - root - INFO - Data is up to date for ADA/USDT
2025-07-16 06:05:05,792 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:05,828 - [KRAKEN] - root - INFO - Loaded 2157 rows of LINK/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:05,828 - [KRAKEN] - root - INFO - Last timestamp in cache for LINK/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,829 - [KRAKEN] - root - INFO - Expected last timestamp for LINK/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,829 - [KRAKEN] - root - INFO - Data is up to date for LINK/USDT
2025-07-16 06:05:05,832 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:05,869 - [KRAKEN] - root - INFO - Loaded 2157 rows of TRX/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:05,869 - [KRAKEN] - root - INFO - Last timestamp in cache for TRX/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,870 - [KRAKEN] - root - INFO - Expected last timestamp for TRX/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,870 - [KRAKEN] - root - INFO - Data is up to date for TRX/USDT
2025-07-16 06:05:05,871 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:05,889 - [KRAKEN] - root - INFO - Loaded 803 rows of PEPE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:05,890 - [KRAKEN] - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,892 - [KRAKEN] - root - INFO - Expected last timestamp for PEPE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,893 - [KRAKEN] - root - INFO - Data is up to date for PEPE/USDT
2025-07-16 06:05:05,893 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:05,928 - [KRAKEN] - root - INFO - Loaded 2157 rows of DOGE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:05,928 - [KRAKEN] - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,930 - [KRAKEN] - root - INFO - Expected last timestamp for DOGE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,931 - [KRAKEN] - root - INFO - Data is up to date for DOGE/USDT
2025-07-16 06:05:05,931 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:05,961 - [KRAKEN] - root - INFO - Loaded 2157 rows of BNB/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:05,963 - [KRAKEN] - root - INFO - Last timestamp in cache for BNB/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,964 - [KRAKEN] - root - INFO - Expected last timestamp for BNB/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,964 - [KRAKEN] - root - INFO - Data is up to date for BNB/USDT
2025-07-16 06:05:05,965 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:05,993 - [KRAKEN] - root - INFO - Loaded 1793 rows of DOT/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:05,995 - [KRAKEN] - root - INFO - Last timestamp in cache for DOT/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,996 - [KRAKEN] - root - INFO - Expected last timestamp for DOT/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:05,996 - [KRAKEN] - root - INFO - Data is up to date for DOT/USDT
2025-07-16 06:05:05,997 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:05,998 - [KRAKEN] - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-07-16 06:05:05,999 - [KRAKEN] - root - INFO - MTPI Multi-Indicator Configuration:
2025-07-16 06:05:05,999 - [KRAKEN] - root - INFO -   - Number of indicators: 8
2025-07-16 06:05:05,999 - [KRAKEN] - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 06:05:05,999 - [KRAKEN] - root - INFO -   - Combination method: consensus
2025-07-16 06:05:05,999 - [KRAKEN] - root - INFO -   - Long threshold: 0.1
2025-07-16 06:05:05,999 - [KRAKEN] - root - INFO -   - Short threshold: -0.1
2025-07-16 06:05:05,999 - [KRAKEN] - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-16 06:05:05,999 - [KRAKEN] - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['BTC/EUR', 'ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:05:05,999 - [KRAKEN] - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-07-16 06:05:05,999 - [KRAKEN] - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-07-16 06:05:05,999 - [KRAKEN] - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-07-16 06:05:05,999 - [KRAKEN] - root - INFO - Parameters: use_mtpi_signal=True, mtpi_indicator_type=PGO
2025-07-16 06:05:05,999 - [KRAKEN] - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-07-16 06:05:05,999 - [KRAKEN] - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-07-16 06:05:05,999 - [KRAKEN] - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-07-16 06:05:05,999 - [KRAKEN] - root - INFO - n_assets=1, use_weighted_allocation=False
2025-07-16 06:05:05,999 - [KRAKEN] - root - INFO - Using provided trend method: PGO For Loop
2025-07-16 06:05:05,999 - [KRAKEN] - root - INFO - Loading configuration from /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/config/settings.yaml...
2025-07-16 06:05:06,032 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:06,032 - [KRAKEN] - root - INFO - Saving configuration to /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/config/settings.yaml...
2025-07-16 06:05:06,048 - [KRAKEN] - root - INFO - Configuration saved successfully.
2025-07-16 06:05:06,050 - [KRAKEN] - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-16 06:05:06,051 - [KRAKEN] - root - INFO - Number of trend detection assets: 14
2025-07-16 06:05:06,053 - [KRAKEN] - root - INFO - Selected assets type: <class 'list'>
2025-07-16 06:05:06,053 - [KRAKEN] - root - INFO - Trading execution assets (EUR): ['BTC/EUR', 'ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:05:06,053 - [KRAKEN] - root - INFO - Number of trading assets: 14
2025-07-16 06:05:06,053 - [KRAKEN] - root - INFO - Trading assets type: <class 'list'>
2025-07-16 06:05:06,356 - [KRAKEN] - root - WARNING - Failed to send with Markdown formatting: Unknown error in HTTP implementation: RuntimeError('<asyncio.locks.Event object at 0x705349b12ba0 [unset]> is bound to a different event loop')
2025-07-16 06:05:06,444 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:05:06,479 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:06,506 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7000566483:AAEJRUb8vgIBBiC75kBGVhiRMEkoSQ2yRig/sendMessage "HTTP/1.1 200 OK"
2025-07-16 06:05:06,523 - [KRAKEN] - root - INFO - Loading configuration from /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/config/settings.yaml...
2025-07-16 06:05:06,566 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:06,566 - [KRAKEN] - root - INFO - Execution context: backtesting
2025-07-16 06:05:06,566 - [KRAKEN] - root - INFO - Execution timing: candle_close
2025-07-16 06:05:06,566 - [KRAKEN] - root - INFO - Ratio calculation method: independent
2025-07-16 06:05:06,566 - [KRAKEN] - root - INFO - Tie-breaking strategy: incumbent
2025-07-16 06:05:06,566 - [KRAKEN] - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-07-16 06:05:06,566 - [KRAKEN] - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 06:05:06,566 - [KRAKEN] - root - INFO - MTPI combination method override: consensus
2025-07-16 06:05:06,567 - [KRAKEN] - root - INFO - MTPI long threshold override: 0.1
2025-07-16 06:05:06,567 - [KRAKEN] - root - INFO - MTPI short threshold override: -0.1
2025-07-16 06:05:06,567 - [KRAKEN] - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-07-16 06:05:06,567 - [KRAKEN] - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-07-16 06:05:06,569 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:06,569 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:06,569 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:06,569 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:06,570 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:06,570 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:06,570 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:06,570 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:06,570 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:06,570 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:06,570 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:06,571 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:06,571 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:06,571 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:06,571 - [KRAKEN] - root - INFO - Checking cache for 14 symbols (1d)...
2025-07-16 06:05:06,594 - [KRAKEN] - root - INFO - Loaded 216 rows of ETH/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:06,595 - [KRAKEN] - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:06,596 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:06,596 - [KRAKEN] - root - INFO - Loaded 216 rows of ETH/USDT data from cache (after filtering).
2025-07-16 06:05:06,618 - [KRAKEN] - root - INFO - Loaded 216 rows of BTC/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:06,619 - [KRAKEN] - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:06,619 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:06,619 - [KRAKEN] - root - INFO - Loaded 216 rows of BTC/USDT data from cache (after filtering).
2025-07-16 06:05:06,635 - [KRAKEN] - root - INFO - Loaded 216 rows of SOL/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:06,636 - [KRAKEN] - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:06,636 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:06,636 - [KRAKEN] - root - INFO - Loaded 216 rows of SOL/USDT data from cache (after filtering).
2025-07-16 06:05:06,647 - [KRAKEN] - root - INFO - Loaded 216 rows of SUI/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:06,648 - [KRAKEN] - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:06,648 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:06,648 - [KRAKEN] - root - INFO - Loaded 216 rows of SUI/USDT data from cache (after filtering).
2025-07-16 06:05:06,670 - [KRAKEN] - root - INFO - Loaded 216 rows of XRP/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:06,671 - [KRAKEN] - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:06,671 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:06,671 - [KRAKEN] - root - INFO - Loaded 216 rows of XRP/USDT data from cache (after filtering).
2025-07-16 06:05:06,686 - [KRAKEN] - root - INFO - Loaded 216 rows of AAVE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:06,688 - [KRAKEN] - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:06,688 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:06,688 - [KRAKEN] - root - INFO - Loaded 216 rows of AAVE/USDT data from cache (after filtering).
2025-07-16 06:05:06,706 - [KRAKEN] - root - INFO - Loaded 216 rows of AVAX/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:06,707 - [KRAKEN] - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:06,707 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:06,708 - [KRAKEN] - root - INFO - Loaded 216 rows of AVAX/USDT data from cache (after filtering).
2025-07-16 06:05:06,734 - [KRAKEN] - root - INFO - Loaded 216 rows of ADA/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:06,736 - [KRAKEN] - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:06,739 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:06,739 - [KRAKEN] - root - INFO - Loaded 216 rows of ADA/USDT data from cache (after filtering).
2025-07-16 06:05:06,784 - [KRAKEN] - root - INFO - Loaded 216 rows of LINK/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:06,785 - [KRAKEN] - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:06,788 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:06,788 - [KRAKEN] - root - INFO - Loaded 216 rows of LINK/USDT data from cache (after filtering).
2025-07-16 06:05:06,828 - [KRAKEN] - root - INFO - Loaded 216 rows of TRX/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:06,831 - [KRAKEN] - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:06,832 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:06,832 - [KRAKEN] - root - INFO - Loaded 216 rows of TRX/USDT data from cache (after filtering).
2025-07-16 06:05:06,850 - [KRAKEN] - root - INFO - Loaded 216 rows of PEPE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:06,851 - [KRAKEN] - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:06,851 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:06,852 - [KRAKEN] - root - INFO - Loaded 216 rows of PEPE/USDT data from cache (after filtering).
2025-07-16 06:05:06,871 - [KRAKEN] - root - INFO - Loaded 216 rows of DOGE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:06,872 - [KRAKEN] - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:06,873 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:06,873 - [KRAKEN] - root - INFO - Loaded 216 rows of DOGE/USDT data from cache (after filtering).
2025-07-16 06:05:06,896 - [KRAKEN] - root - INFO - Loaded 216 rows of BNB/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:06,897 - [KRAKEN] - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:06,897 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:06,897 - [KRAKEN] - root - INFO - Loaded 216 rows of BNB/USDT data from cache (after filtering).
2025-07-16 06:05:06,914 - [KRAKEN] - root - INFO - Loaded 216 rows of DOT/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:06,915 - [KRAKEN] - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:06,916 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:06,916 - [KRAKEN] - root - INFO - Loaded 216 rows of DOT/USDT data from cache (after filtering).
2025-07-16 06:05:06,916 - [KRAKEN] - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-16 06:05:06,916 - [KRAKEN] - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:06,916 - [KRAKEN] - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:06,917 - [KRAKEN] - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:06,917 - [KRAKEN] - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:06,917 - [KRAKEN] - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:06,917 - [KRAKEN] - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:06,917 - [KRAKEN] - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:06,917 - [KRAKEN] - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:06,917 - [KRAKEN] - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:06,917 - [KRAKEN] - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:06,917 - [KRAKEN] - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:06,918 - [KRAKEN] - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:06,918 - [KRAKEN] - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:06,918 - [KRAKEN] - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:06,949 - [KRAKEN] - root - INFO - Using standard MTPI warmup period of 120 days
2025-07-16 06:05:06,950 - [KRAKEN] - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-07-16 06:05:06,950 - [KRAKEN] - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-07-16 06:05:06,950 - [KRAKEN] - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-07-16 06:05:06,950 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:05:06,962 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:06,963 - [KRAKEN] - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-07-16 06:05:06,963 - [KRAKEN] - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-07-16 06:05:06,963 - [KRAKEN] - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 06:05:06,963 - [KRAKEN] - root - INFO - Override: combination_method = consensus
2025-07-16 06:05:06,963 - [KRAKEN] - root - INFO - Override: long_threshold = 0.1
2025-07-16 06:05:06,963 - [KRAKEN] - root - INFO - Override: short_threshold = -0.1
2025-07-16 06:05:06,963 - [KRAKEN] - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-07-16 06:05:06,963 - [KRAKEN] - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-16 06:05:06,963 - [KRAKEN] - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-07-16 06:05:06,963 - [KRAKEN] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-16 06:05:06,984 - [KRAKEN] - root - INFO - Loaded 276 rows of BTC/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:06,984 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:06,984 - [KRAKEN] - root - INFO - Loaded 276 rows of BTC/USDT data from cache (after filtering).
2025-07-16 06:05:06,984 - [KRAKEN] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-16 06:05:06,984 - [KRAKEN] - root - INFO - Fetched BTC data: 276 candles from 2024-10-13 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:06,985 - [KRAKEN] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-16 06:05:07,069 - [KRAKEN] - root - INFO - Generated PGO Score signals: {-1: np.int64(111), 0: np.int64(34), 1: np.int64(131)}
2025-07-16 06:05:07,069 - [KRAKEN] - root - INFO - Generated pgo signals: 276 values
2025-07-16 06:05:07,071 - [KRAKEN] - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-07-16 06:05:07,071 - [KRAKEN] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-16 06:05:07,111 - [KRAKEN] - root - INFO - Generated BB Score signals: {-1: np.int64(109), 0: np.int64(32), 1: np.int64(135)}
2025-07-16 06:05:07,112 - [KRAKEN] - root - INFO - Generated Bollinger Band signals: 276 values
2025-07-16 06:05:07,112 - [KRAKEN] - root - INFO - Generated bollinger_bands signals: 276 values
2025-07-16 06:05:08,336 - [KRAKEN] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-16 06:05:08,337 - [KRAKEN] - root - INFO - Generated dwma_score signals: 276 values
2025-07-16 06:05:08,430 - [KRAKEN] - root - INFO - Generated DEMA Supertrend signals
2025-07-16 06:05:08,430 - [KRAKEN] - root - INFO - Signal distribution: {-1: np.int64(156), 0: np.int64(1), 1: np.int64(119)}
2025-07-16 06:05:08,430 - [KRAKEN] - root - INFO - Generated DEMA Super Score signals
2025-07-16 06:05:08,430 - [KRAKEN] - root - INFO - Generated dema_super_score signals: 276 values
2025-07-16 06:05:08,744 - [KRAKEN] - root - INFO - Generated DPSD signals
2025-07-16 06:05:08,744 - [KRAKEN] - root - INFO - Signal distribution: {-1: np.int64(105), 0: np.int64(87), 1: np.int64(84)}
2025-07-16 06:05:08,744 - [KRAKEN] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-16 06:05:08,744 - [KRAKEN] - root - INFO - Generated dpsd_score signals: 276 values
2025-07-16 06:05:08,761 - [KRAKEN] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-16 06:05:08,761 - [KRAKEN] - root - INFO - Generated AAD Score signals using SMA method
2025-07-16 06:05:08,762 - [KRAKEN] - root - INFO - Generated aad_score signals: 276 values
2025-07-16 06:05:08,908 - [KRAKEN] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-16 06:05:08,909 - [KRAKEN] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-16 06:05:08,909 - [KRAKEN] - root - INFO - Generated dynamic_ema_score signals: 276 values
2025-07-16 06:05:09,167 - [KRAKEN] - root - INFO - Generated quantile_dema_score signals: 276 values
2025-07-16 06:05:09,194 - [KRAKEN] - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-07-16 06:05:09,198 - [KRAKEN] - root - INFO - Signal distribution: {1: 151, -1: 124, 0: 1}
2025-07-16 06:05:09,198 - [KRAKEN] - root - INFO - Generated combined MTPI signals: 276 values using consensus method
2025-07-16 06:05:09,198 - [KRAKEN] - root - INFO - Signal distribution: {1: 151, -1: 124, 0: 1}
2025-07-16 06:05:09,199 - [KRAKEN] - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-07-16 06:05:09,208 - [KRAKEN] - root - INFO - Saving configuration to config/settings.yaml...
2025-07-16 06:05:09,230 - [KRAKEN] - root - INFO - Configuration saved successfully.
2025-07-16 06:05:09,230 - [KRAKEN] - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-07-16 06:05:09,231 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:05:09,260 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:09,260 - [KRAKEN] - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-07-16 06:05:09,260 - [KRAKEN] - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-07-16 06:05:09,260 - [KRAKEN] - root - INFO - Using ratio calculation method: independent
2025-07-16 06:05:09,351 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:09,405 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:09,455 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:09,455 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:09,533 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:09,543 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:09,576 - [KRAKEN] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-16 06:05:09,577 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:09,605 - [KRAKEN] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-16 06:05:09,626 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:09,701 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:09,703 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:09,766 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:09,791 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:09,872 - [KRAKEN] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:09,875 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:09,910 - [KRAKEN] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:09,919 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:09,966 - [KRAKEN] - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-07-16 06:05:09,966 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:10,014 - [KRAKEN] - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-07-16 06:05:10,034 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:10,108 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:10,109 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:10,179 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:10,189 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:10,245 - [KRAKEN] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:05:10,246 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:10,295 - [KRAKEN] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:05:10,321 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:10,395 - [KRAKEN] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:05:10,398 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:10,456 - [KRAKEN] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:05:10,479 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:10,552 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:10,555 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:10,621 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:10,642 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:10,719 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:10,721 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:10,787 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:10,811 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:10,890 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:10,986 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:11,064 - [KRAKEN] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-16 06:05:11,066 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:11,130 - [KRAKEN] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-16 06:05:11,154 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:11,221 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:11,223 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:11,283 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:11,312 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:11,399 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:11,482 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:11,556 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:11,558 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:11,620 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:11,640 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:11,707 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:11,707 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:11,779 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:11,800 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:11,879 - [KRAKEN] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:11,879 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:11,939 - [KRAKEN] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:11,958 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:12,041 - [KRAKEN] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-16 06:05:12,041 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:12,105 - [KRAKEN] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-16 06:05:12,125 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:12,193 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:12,193 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:12,254 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:12,274 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:12,342 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:12,342 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:12,405 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:12,422 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:12,506 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:12,508 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:12,576 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:12,594 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:12,661 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:12,661 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:12,727 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:12,748 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:12,818 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:12,895 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:12,964 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:13,038 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:13,102 - [KRAKEN] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-16 06:05:13,102 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:13,162 - [KRAKEN] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-16 06:05:13,182 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:13,247 - [KRAKEN] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-16 06:05:13,249 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:13,315 - [KRAKEN] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-16 06:05:13,335 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:13,411 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:13,492 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:13,567 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:13,568 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:13,634 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:13,654 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:13,726 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:13,727 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:13,788 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:13,808 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:13,927 - [KRAKEN] - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-16 06:05:13,930 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:14,007 - [KRAKEN] - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-16 06:05:14,027 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:14,105 - [KRAKEN] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:05:14,107 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:14,188 - [KRAKEN] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:05:14,219 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:14,299 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:14,299 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:14,373 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:14,397 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:14,471 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:14,471 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:14,531 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:14,550 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:14,621 - [KRAKEN] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-16 06:05:14,624 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:14,683 - [KRAKEN] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-16 06:05:14,705 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:14,771 - [KRAKEN] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:05:14,773 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:14,836 - [KRAKEN] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:05:14,856 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:14,952 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:14,954 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:15,019 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:15,051 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:15,166 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:15,167 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:15,269 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:15,301 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:15,418 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:15,420 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:15,522 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:15,555 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:15,719 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:15,723 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:15,855 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:15,890 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:16,009 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:16,154 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:16,274 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:16,414 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:16,487 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:16,572 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:16,647 - [KRAKEN] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-16 06:05:16,648 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:16,707 - [KRAKEN] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-16 06:05:16,729 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:16,798 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:16,882 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:16,958 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:17,046 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:17,126 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:17,126 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:17,198 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:17,226 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:17,308 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:17,412 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:17,486 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:17,616 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:17,734 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:17,736 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:17,813 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:17,835 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:17,914 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,013 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:18,098 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,183 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:18,258 - [KRAKEN] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-16 06:05:18,260 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,339 - [KRAKEN] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-16 06:05:18,367 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:18,458 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,548 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:18,624 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,714 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:18,795 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:18,795 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,864 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:18,884 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:18,954 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:18,956 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:19,017 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:19,036 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:19,110 - [KRAKEN] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-16 06:05:19,110 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:19,174 - [KRAKEN] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-16 06:05:19,196 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:19,276 - [KRAKEN] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:19,278 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:19,354 - [KRAKEN] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:19,389 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:19,470 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:19,473 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:19,553 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:19,572 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:19,647 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:19,650 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:19,713 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:19,733 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:19,813 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:19,814 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:19,889 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:19,909 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:19,980 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:20,089 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:20,177 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:20,317 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:20,433 - [KRAKEN] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:20,436 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:20,504 - [KRAKEN] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:20,526 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:20,594 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:20,678 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:20,749 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:20,830 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:20,905 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:20,996 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:21,069 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:21,153 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:21,226 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:21,310 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:21,402 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:21,402 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:21,468 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:21,489 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:21,606 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:21,608 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:21,705 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:21,727 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:21,792 - [KRAKEN] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:21,794 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:21,851 - [KRAKEN] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:21,871 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:21,933 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:22,013 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:22,078 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:22,157 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:22,225 - [KRAKEN] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:22,227 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:22,287 - [KRAKEN] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:22,308 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:22,373 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:22,467 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:22,547 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:22,547 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:22,619 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:22,639 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:22,710 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:22,807 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:22,878 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:22,980 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:23,062 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:23,159 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:23,240 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:23,335 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:23,421 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:23,529 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:23,610 - [KRAKEN] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-16 06:05:23,613 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:23,688 - [KRAKEN] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-16 06:05:23,711 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:23,800 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:23,895 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:23,974 - [KRAKEN] - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-07-16 06:05:23,975 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:24,039 - [KRAKEN] - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-07-16 06:05:24,060 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:24,146 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:24,235 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:24,318 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:24,411 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:24,511 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:24,600 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:24,670 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:24,753 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:24,834 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:24,914 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:24,988 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:24,988 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:25,085 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:25,108 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:25,184 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:25,266 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:25,337 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:25,421 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:25,491 - [KRAKEN] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:25,493 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:25,560 - [KRAKEN] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:25,581 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:25,662 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:25,750 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:25,827 - [KRAKEN] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:05:25,829 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:25,901 - [KRAKEN] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:05:25,921 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:25,987 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:25,989 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:26,073 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:26,116 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:26,200 - [KRAKEN] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-16 06:05:26,202 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:26,274 - [KRAKEN] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-16 06:05:26,298 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:26,379 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:26,494 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:26,565 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:26,565 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:26,627 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:26,660 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:26,782 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:26,785 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:26,883 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:26,910 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:26,983 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:27,071 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:27,139 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:27,141 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:27,202 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:27,225 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:27,314 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:27,407 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:27,480 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:27,576 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:27,649 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:27,740 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:27,848 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:27,983 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:28,084 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:28,210 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:28,283 - [KRAKEN] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-16 06:05:28,283 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:28,353 - [KRAKEN] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-16 06:05:28,374 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:28,454 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:28,538 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:28,611 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:28,699 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:28,784 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:28,873 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:28,953 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:28,955 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:29,028 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:29,051 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:29,168 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:29,329 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:29,444 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:29,542 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:29,620 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:29,712 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:29,789 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:29,898 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:29,980 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:30,066 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:30,144 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:30,145 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:30,213 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:30,237 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:30,319 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:30,428 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:30,517 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:30,520 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:30,586 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:30,609 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:30,679 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:30,680 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:30,740 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:30,759 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:30,824 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:30,824 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:30,896 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:30,915 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:30,986 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:31,066 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:31,137 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:31,138 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:31,195 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:31,217 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:31,281 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:31,282 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:31,339 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:31,359 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:31,436 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:31,513 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:31,579 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:31,660 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:31,729 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:31,822 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:31,886 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:31,983 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:32,063 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:32,140 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:32,218 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:32,309 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:32,387 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:32,464 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:32,565 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:32,647 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:32,731 - [KRAKEN] - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-07-16 06:05:32,731 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:32,790 - [KRAKEN] - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-07-16 06:05:32,810 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:32,891 - [KRAKEN] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:32,892 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:32,953 - [KRAKEN] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:32,973 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:33,045 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:33,048 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:33,112 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:33,132 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:33,208 - [KRAKEN] - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-07-16 06:05:33,209 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:33,278 - [KRAKEN] - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-07-16 06:05:33,299 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:33,367 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:33,448 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:33,529 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:33,612 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:33,685 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:33,769 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:33,852 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:33,971 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:34,084 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:34,163 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:34,255 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:34,371 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:34,439 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:34,524 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:34,591 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:34,592 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:34,663 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:34,685 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:34,754 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:34,757 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:34,817 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:34,838 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:34,907 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:34,909 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:34,978 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:35,007 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:35,097 - [KRAKEN] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-16 06:05:35,097 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:35,157 - [KRAKEN] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-16 06:05:35,177 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:35,246 - [KRAKEN] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-16 06:05:35,248 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:35,306 - [KRAKEN] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-16 06:05:35,327 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:35,395 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:35,398 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:35,459 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:35,490 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:35,570 - [KRAKEN] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:35,573 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:35,643 - [KRAKEN] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:35,662 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:35,731 - [KRAKEN] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-16 06:05:35,733 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:35,798 - [KRAKEN] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-16 06:05:35,818 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:35,891 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:35,967 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:36,035 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:36,036 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:36,095 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:36,114 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:36,181 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:36,261 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:36,330 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:36,422 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:36,537 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:36,539 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:36,605 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:36,626 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:36,720 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:36,720 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:36,787 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:36,808 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:36,901 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:36,902 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:36,977 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:37,000 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:37,069 - [KRAKEN] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:05:37,069 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:37,137 - [KRAKEN] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:05:37,158 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:37,252 - [KRAKEN] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-16 06:05:37,253 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:37,338 - [KRAKEN] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-16 06:05:37,357 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:37,422 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:37,423 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:37,498 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:37,530 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:37,618 - [KRAKEN] - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-07-16 06:05:37,619 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:37,678 - [KRAKEN] - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-07-16 06:05:37,699 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:37,771 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:37,773 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:37,834 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:37,853 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:37,923 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:37,923 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:37,981 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:38,000 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:38,071 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:38,159 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:38,228 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:38,314 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:38,389 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:38,473 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:38,586 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:38,661 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:38,729 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:38,804 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:38,876 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:38,880 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:38,941 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:38,965 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:39,037 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:39,037 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:39,102 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:39,123 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:39,191 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:39,192 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:39,259 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:39,277 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:39,347 - [KRAKEN] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-16 06:05:39,350 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:39,406 - [KRAKEN] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-16 06:05:39,426 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:39,493 - [KRAKEN] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-16 06:05:39,493 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:39,554 - [KRAKEN] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-16 06:05:39,573 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:39,641 - [KRAKEN] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:39,641 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:39,701 - [KRAKEN] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:39,720 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:39,788 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:39,870 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:39,939 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:39,941 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:40,000 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:40,021 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:40,100 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:40,181 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:40,250 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:40,331 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:49,183 - [KRAKEN] - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-07-16 06:05:49,185 - [KRAKEN] - root - INFO - Latest MTPI signal is 1
2025-07-16 06:05:49,185 - [KRAKEN] - root - INFO - Latest MTPI signal is bullish (1), will proceed with normal asset selection
2025-07-16 06:05:49,185 - [KRAKEN] - root - INFO - Finished calculating daily scores. DataFrame shape: (216, 14)
2025-07-16 06:05:49,186 - [KRAKEN] - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering ENABLED
2025-07-16 06:05:49,200 - [KRAKEN] - root - INFO - Date ranges for each asset:
2025-07-16 06:05:49,204 - [KRAKEN] - root - INFO -   ETH/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:49,204 - [KRAKEN] - root - INFO -   BTC/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:49,204 - [KRAKEN] - root - INFO -   SOL/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:49,204 - [KRAKEN] - root - INFO -   SUI/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:49,204 - [KRAKEN] - root - INFO -   XRP/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:49,204 - [KRAKEN] - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:49,204 - [KRAKEN] - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:49,204 - [KRAKEN] - root - INFO -   ADA/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:49,204 - [KRAKEN] - root - INFO -   LINK/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:49,204 - [KRAKEN] - root - INFO -   TRX/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:49,205 - [KRAKEN] - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:49,205 - [KRAKEN] - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:49,205 - [KRAKEN] - root - INFO -   BNB/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:49,205 - [KRAKEN] - root - INFO -   DOT/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:49,205 - [KRAKEN] - root - INFO - Common dates range: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:49,209 - [KRAKEN] - root - INFO - Analysis will run from: 2025-02-10 to 2025-07-15 (156 candles)
2025-07-16 06:05:49,222 - [KRAKEN] - root - INFO - EXECUTION TIMING VERIFICATION:
2025-07-16 06:05:49,222 - [KRAKEN] - root - INFO -    Execution Method: candle_close
2025-07-16 06:05:49,222 - [KRAKEN] - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-07-16 06:05:49,222 - [KRAKEN] - root - INFO -    Signal generated and executed immediately
2025-07-16 06:05:49,249 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-10 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 10.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 8.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:49,250 - [KRAKEN] - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,250 - [KRAKEN] - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,250 - [KRAKEN] - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,250 - [KRAKEN] - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,250 - [KRAKEN] - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,250 - [KRAKEN] - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,250 - [KRAKEN] - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,250 - [KRAKEN] - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,250 - [KRAKEN] - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,250 - [KRAKEN] - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,250 - [KRAKEN] - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,251 - [KRAKEN] - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,251 - [KRAKEN] - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,251 - [KRAKEN] - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,251 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,251 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,251 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,251 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,258 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-11 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 9.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:49,259 - [KRAKEN] - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,259 - [KRAKEN] - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,259 - [KRAKEN] - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,259 - [KRAKEN] - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,259 - [KRAKEN] - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,259 - [KRAKEN] - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,259 - [KRAKEN] - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,259 - [KRAKEN] - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,260 - [KRAKEN] - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,260 - [KRAKEN] - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,260 - [KRAKEN] - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,260 - [KRAKEN] - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,260 - [KRAKEN] - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,260 - [KRAKEN] - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,260 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,260 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,263 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,263 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,268 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-12 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 9.0, 'SUI/USDT': 4.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:49,269 - [KRAKEN] - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,269 - [KRAKEN] - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,269 - [KRAKEN] - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,269 - [KRAKEN] - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,272 - [KRAKEN] - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,272 - [KRAKEN] - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,273 - [KRAKEN] - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,273 - [KRAKEN] - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,273 - [KRAKEN] - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,273 - [KRAKEN] - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,273 - [KRAKEN] - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,273 - [KRAKEN] - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,273 - [KRAKEN] - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,273 - [KRAKEN] - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,273 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,273 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,273 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,273 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,276 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-13 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:49,278 - [KRAKEN] - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,278 - [KRAKEN] - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,278 - [KRAKEN] - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,279 - [KRAKEN] - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,279 - [KRAKEN] - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,279 - [KRAKEN] - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,279 - [KRAKEN] - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,279 - [KRAKEN] - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,279 - [KRAKEN] - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,279 - [KRAKEN] - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,279 - [KRAKEN] - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,279 - [KRAKEN] - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,279 - [KRAKEN] - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,280 - [KRAKEN] - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,280 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,280 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,280 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,280 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,282 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-14 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:49,282 - [KRAKEN] - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,282 - [KRAKEN] - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,282 - [KRAKEN] - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,282 - [KRAKEN] - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,282 - [KRAKEN] - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,282 - [KRAKEN] - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,285 - [KRAKEN] - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,286 - [KRAKEN] - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,286 - [KRAKEN] - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,286 - [KRAKEN] - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,286 - [KRAKEN] - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,286 - [KRAKEN] - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,286 - [KRAKEN] - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,286 - [KRAKEN] - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:49,286 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,286 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,286 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,286 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,291 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-15 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:49,293 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,293 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,293 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,294 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,295 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-16 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 5.0}
2025-07-16 06:05:49,296 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,296 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,296 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,296 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,300 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-17 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:49,302 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,302 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,302 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,303 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,304 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-18 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:49,308 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,308 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,308 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,308 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,313 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-19 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 5.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 8.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:49,313 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,313 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,313 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,313 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,315 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-20 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 8.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 5.0}
2025-07-16 06:05:49,316 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,316 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,316 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,316 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,323 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-21 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:49,323 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,323 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,323 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,323 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,325 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-22 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 6.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 13.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:49,327 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,327 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,328 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,328 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,330 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-23 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 5.0}
2025-07-16 06:05:49,330 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,330 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,333 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,333 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,337 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-24 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:49,338 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,339 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,339 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,339 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,343 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-25 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 2.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:49,344 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,344 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,344 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,344 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,349 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-26 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:49,350 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,350 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,350 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,350 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,351 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-27 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 5.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 5.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 10.0}
2025-07-16 06:05:49,355 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,355 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,356 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,356 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,357 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-28 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 5.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 12.0, 'DOT/USDT': 11.0}
2025-07-16 06:05:49,361 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,361 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,361 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,362 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,363 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-01 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 2.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 11.0}
2025-07-16 06:05:49,363 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,367 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,367 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,367 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,369 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-02 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 1.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 9.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:49,369 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,373 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,373 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,374 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,379 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-03 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 2.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 9.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:49,379 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,380 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,380 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,380 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,381 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-04 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 1.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 13.0, 'LINK/USDT': 2.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,385 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,386 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,386 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,386 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,392 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-05 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 1.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 7.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,392 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,392 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,392 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,392 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,398 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-06 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:49,398 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,398 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,398 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,398 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,404 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-07 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:49,404 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,404 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,404 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,404 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,409 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-08 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,410 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,410 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,410 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,410 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,411 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-09 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,413 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,413 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,414 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,414 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,415 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-10 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,415 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,417 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,417 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,417 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,418 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-11 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,419 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,419 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,419 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,419 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,422 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-12 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,422 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,423 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,423 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,423 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,426 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-13 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 9.0, 'SOL/USDT': 3.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,426 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,426 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,426 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,426 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,430 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-14 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 6.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,430 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,430 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,430 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,430 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,433 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-15 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 6.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 11.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,434 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,434 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,434 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,434 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,435 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-16 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 10.0, 'LINK/USDT': 5.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 12.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,435 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,437 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,437 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,437 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,438 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-17 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 10.0, 'LINK/USDT': 5.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,439 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,439 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,439 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,439 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,442 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-18 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 9.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:49,442 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,442 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,443 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,443 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,446 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-19 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 9.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:49,446 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,446 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,446 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,446 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,449 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-20 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:49,449 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,450 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,450 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,450 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,451 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-21 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:49,451 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,451 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,453 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,453 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,454 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-22 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:49,455 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,455 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,455 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,455 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,458 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-23 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:49,458 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,458 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,458 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,458 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,461 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-24 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 0.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 13.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,462 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,462 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,462 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,462 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,463 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-25 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 5.0, 'SOL/USDT': 4.0, 'SUI/USDT': 0.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,463 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,463 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,465 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,465 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,466 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-26 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 10.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:49,467 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,467 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,467 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,467 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,470 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-27 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 9.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:49,470 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,470 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,470 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,471 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,474 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-28 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:49,474 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,474 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,474 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,474 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,477 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-29 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:49,478 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,478 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,478 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,478 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,479 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-30 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:49,479 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,481 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,481 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,481 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,482 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-31 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 7.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:49,483 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,483 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,483 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,483 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,486 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-01 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 7.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:49,486 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,486 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,487 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,487 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,490 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-02 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:49,491 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,493 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,493 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,493 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,494 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-03 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:49,495 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,495 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,495 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,495 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,496 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-04 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 8.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:49,499 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,499 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,499 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,499 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,501 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-05 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 8.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:49,501 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,501 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,501 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,501 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,504 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-06 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 5.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 12.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,505 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,505 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,505 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,505 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,508 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-07 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 11.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:49,508 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,508 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,508 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,508 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,512 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-08 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 5.0, 'SUI/USDT': 5.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 5.0}
2025-07-16 06:05:49,513 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,513 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,513 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,513 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,516 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-09 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 1.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,516 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,517 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,517 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,517 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,518 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-10 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,518 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,518 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,518 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,520 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,522 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-11 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,522 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,524 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,524 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,524 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,526 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-12 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,526 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,526 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,529 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,529 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,531 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-13 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,531 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,531 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,533 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,533 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,535 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-14 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,535 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,535 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,535 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,535 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,538 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-15 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,538 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,539 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,539 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,539 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,543 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-16 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,543 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,543 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,543 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,543 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,548 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-17 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 12.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,548 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,549 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,549 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,549 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,550 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-18 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 12.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,552 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,552 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,552 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,552 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,554 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-19 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 4.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,554 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,554 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,556 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,556 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,558 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-20 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 3.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 5.0}
2025-07-16 06:05:49,558 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,558 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,558 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,558 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,563 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-21 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 3.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:49,563 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,564 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,564 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,564 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,567 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-22 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 12.0, 'SUI/USDT': 9.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 5.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 5.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,568 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,568 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,568 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,569 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,569 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-04-23:
2025-07-16 06:05:49,569 - [KRAKEN] - root - INFO -    Signal Date: 2025-04-22 (generated at 00:00 UTC)
2025-07-16 06:05:49,569 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-23 00:00 UTC (immediate)
2025-07-16 06:05:49,570 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:49,570 - [KRAKEN] - root - INFO -    Buying: ['SOL/USDT']
2025-07-16 06:05:49,570 - [KRAKEN] - root - INFO -    SOL/USDT buy price: $151.1000 (close price)
2025-07-16 06:05:49,572 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-23 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,572 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,572 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,573 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,573 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,573 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-07-16 06:05:49,574 - [KRAKEN] - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-07-16 06:05:49,574 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-07-16 06:05:49,574 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:49,574 - [KRAKEN] - root - INFO -    Selling: ['SOL/USDT']
2025-07-16 06:05:49,574 - [KRAKEN] - root - INFO -    Buying: ['SUI/USDT']
2025-07-16 06:05:49,575 - [KRAKEN] - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-07-16 06:05:49,576 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-24 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:49,576 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,577 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,577 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,577 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,579 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-25 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:49,579 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,579 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,579 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,579 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,581 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-26 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:49,581 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,582 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,582 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,582 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,584 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-27 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:49,584 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,584 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,585 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,585 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,587 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-28 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:49,587 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,587 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,587 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,587 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,589 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-29 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:49,589 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,590 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,590 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,590 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,592 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-30 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:49,592 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,592 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,592 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,593 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,594 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-01 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:49,594 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,594 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,594 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,595 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,596 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-02 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 8.0, 'SOL/USDT': 10.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:49,597 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,597 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,597 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,597 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,599 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-03 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,599 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,599 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,600 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,600 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,601 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-04 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,602 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,602 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,602 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,602 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,604 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-05 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,604 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,604 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,604 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,605 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,606 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-06 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,606 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,606 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,606 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,607 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,608 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-07 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 0.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:49,608 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,609 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,609 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,609 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,611 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-08 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 6.0, 'SOL/USDT': 9.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:49,611 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,611 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,611 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,611 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,612 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-09 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 4.0, 'SOL/USDT': 9.0, 'SUI/USDT': 12.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 1.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:49,612 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,612 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,612 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,613 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,613 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-07-16 06:05:49,613 - [KRAKEN] - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-07-16 06:05:49,613 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-07-16 06:05:49,613 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:49,613 - [KRAKEN] - root - INFO -    Selling: ['SUI/USDT']
2025-07-16 06:05:49,613 - [KRAKEN] - root - INFO -    Buying: ['PEPE/USDT']
2025-07-16 06:05:49,613 - [KRAKEN] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-16 06:05:49,615 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-10 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 12.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,616 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,616 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,616 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,616 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,618 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-11 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 12.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,618 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,618 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,618 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,619 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,620 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-12 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 11.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,621 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,621 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,621 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,621 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,623 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-13 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,623 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,623 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,624 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,624 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,625 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-14 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,625 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,626 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,626 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,626 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,627 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-15 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,628 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,628 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,628 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,629 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,630 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-16 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,630 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,630 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,631 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,631 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,632 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-17 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 4.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,632 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,632 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,633 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,633 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,634 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-18 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 4.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,634 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,634 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,634 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,635 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,636 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-19 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 5.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,636 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,636 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,636 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,637 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,638 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-20 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:49,639 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,639 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,639 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,639 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,639 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-07-16 06:05:49,640 - [KRAKEN] - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-07-16 06:05:49,640 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-07-16 06:05:49,640 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:49,640 - [KRAKEN] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-16 06:05:49,640 - [KRAKEN] - root - INFO -    Buying: ['AAVE/USDT']
2025-07-16 06:05:49,641 - [KRAKEN] - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-07-16 06:05:49,642 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-21 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:49,642 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,642 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,643 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,643 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,643 - [KRAKEN] - root - INFO - [TIE-BREAKING] Incumbent approach: Keeping AAVE/USDT (tied at score 12.0)
2025-07-16 06:05:49,644 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-22 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 1.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:49,645 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,645 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,645 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,645 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,645 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-07-16 06:05:49,645 - [KRAKEN] - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-07-16 06:05:49,645 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-07-16 06:05:49,646 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:49,646 - [KRAKEN] - root - INFO -    Selling: ['AAVE/USDT']
2025-07-16 06:05:49,646 - [KRAKEN] - root - INFO -    Buying: ['PEPE/USDT']
2025-07-16 06:05:49,646 - [KRAKEN] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-16 06:05:49,648 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-23 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 9.0, 'SUI/USDT': 7.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 2.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:49,648 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,648 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,648 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,648 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,650 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-24 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 9.0, 'SUI/USDT': 6.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 2.0, 'LINK/USDT': 1.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 5.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:49,650 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,651 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,651 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,651 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,651 - [KRAKEN] - root - INFO - [TIE-BREAKING] Incumbent approach: Keeping PEPE/USDT (tied at score 12.0)
2025-07-16 06:05:49,655 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-25 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 3.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 5.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,657 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,657 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,657 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,658 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,658 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-05-26:
2025-07-16 06:05:49,658 - [KRAKEN] - root - INFO -    Signal Date: 2025-05-25 (generated at 00:00 UTC)
2025-07-16 06:05:49,658 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-26 00:00 UTC (immediate)
2025-07-16 06:05:49,658 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:49,658 - [KRAKEN] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-16 06:05:49,658 - [KRAKEN] - root - INFO -    Buying: ['AAVE/USDT']
2025-07-16 06:05:49,658 - [KRAKEN] - root - INFO -    AAVE/USDT buy price: $267.5200 (close price)
2025-07-16 06:05:49,662 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-26 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 6.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,663 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,663 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,663 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,663 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,665 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-27 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 7.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,667 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,667 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,668 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,668 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,670 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-28 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 8.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 2.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,672 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,672 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,673 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,673 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,676 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-29 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 2.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,677 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,677 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,677 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,677 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,682 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-30 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,683 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,685 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,685 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,685 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,689 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-31 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,690 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,690 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,690 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,690 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,694 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-01 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,694 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,694 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,694 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,694 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,698 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-02 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,699 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,699 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,699 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,699 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,705 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-03 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,706 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,706 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,706 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,706 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,710 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-04 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,710 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,712 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,712 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,712 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,714 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-05 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:49,714 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,714 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,714 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,714 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,718 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-06 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,719 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,721 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,721 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,721 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,723 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-07 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,723 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,723 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,723 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,723 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,725 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-08 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,726 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,726 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,726 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,726 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,728 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-09 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 1.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,728 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,728 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,728 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,728 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,730 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-10 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,730 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,730 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,730 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,730 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,731 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-11 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,732 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,732 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,732 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,732 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,733 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-12 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,733 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,733 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,734 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,734 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,736 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-13 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 1.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,737 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,737 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,737 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,738 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,739 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-14 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,740 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,740 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,740 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,740 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,741 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-15 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,742 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,742 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,742 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,742 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,744 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-16 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:49,744 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,745 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,745 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,745 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,747 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-17 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:49,748 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,748 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,748 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,748 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,750 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-18 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:49,751 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,751 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,751 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,751 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,753 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-19 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:49,753 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,753 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,754 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,754 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,755 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-20 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:49,756 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,756 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,756 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,756 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,757 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-07-16 06:05:49,757 - [KRAKEN] - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-07-16 06:05:49,757 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-07-16 06:05:49,757 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:49,757 - [KRAKEN] - root - INFO -    Selling: ['AAVE/USDT']
2025-07-16 06:05:49,759 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-21 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 11.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:49,759 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,759 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,760 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,760 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,761 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-22 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,761 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,761 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,761 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,762 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,763 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-23 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,763 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,764 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,764 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,764 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,765 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-24 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,765 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,765 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,766 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,766 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,767 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-25 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,767 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,768 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,768 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,768 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,769 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-26 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,770 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,770 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,770 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,770 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,771 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-27 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,771 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,772 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,772 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,772 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,773 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-28 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 13.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,774 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,774 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,774 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,774 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,776 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-29 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,776 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,778 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,778 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,778 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,780 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-30 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,782 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,782 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,782 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,783 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,784 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-01 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,784 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,784 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,784 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,785 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,788 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-02 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,790 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,790 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,790 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,790 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,794 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-03 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,794 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,794 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,794 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,794 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,797 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-04 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,797 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,798 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,798 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,798 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,799 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-05 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:49,802 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,803 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,803 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,803 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,804 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-06 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:49,806 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,806 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,806 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,807 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,808 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-07 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:49,808 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,808 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,810 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,811 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,812 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-08 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 6.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:49,812 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,812 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,814 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,815 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,816 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-09 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 4.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:49,818 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,819 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,819 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,819 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,822 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-10 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 4.0, 'SUI/USDT': 11.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:49,823 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,823 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,823 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,823 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:49,823 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-07-11:
2025-07-16 06:05:49,823 - [KRAKEN] - root - INFO -    Signal Date: 2025-07-10 (generated at 00:00 UTC)
2025-07-16 06:05:49,823 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-11 00:00 UTC (immediate)
2025-07-16 06:05:49,823 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:49,823 - [KRAKEN] - root - INFO -    Buying: ['SUI/USDT']
2025-07-16 06:05:49,824 - [KRAKEN] - root - INFO -    SUI/USDT buy price: $3.3868 (close price)
2025-07-16 06:05:49,828 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-11 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 3.0, 'SOL/USDT': 3.0, 'SUI/USDT': 10.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 8.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:49,828 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,828 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,828 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,828 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,828 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-07-12:
2025-07-16 06:05:49,828 - [KRAKEN] - root - INFO -    Signal Date: 2025-07-11 (generated at 00:00 UTC)
2025-07-16 06:05:49,828 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-12 00:00 UTC (immediate)
2025-07-16 06:05:49,829 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:49,829 - [KRAKEN] - root - INFO -    Selling: ['SUI/USDT']
2025-07-16 06:05:49,829 - [KRAKEN] - root - INFO -    Buying: ['XRP/USDT']
2025-07-16 06:05:49,829 - [KRAKEN] - root - INFO -    XRP/USDT buy price: $2.7395 (close price)
2025-07-16 06:05:49,833 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-12 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 3.0, 'SUI/USDT': 10.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 9.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:49,835 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,835 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,835 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,836 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,839 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-13 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,839 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,839 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,839 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,840 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:49,843 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-14 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 12.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:49,843 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:49,843 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:49,843 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:49,844 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:50,010 - [KRAKEN] - root - INFO - Entry trade at 2025-04-23 00:00:00+00:00: SOL/USDT
2025-07-16 06:05:50,011 - [KRAKEN] - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-07-16 06:05:50,011 - [KRAKEN] - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-07-16 06:05:50,011 - [KRAKEN] - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-16 06:05:50,012 - [KRAKEN] - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-07-16 06:05:50,012 - [KRAKEN] - root - INFO - Swap trade at 2025-05-26 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-16 06:05:50,015 - [KRAKEN] - root - INFO - Exit trade at 2025-06-21 00:00:00+00:00 from AAVE/USDT
2025-07-16 06:05:50,015 - [KRAKEN] - root - INFO - Entry trade at 2025-07-11 00:00:00+00:00: SUI/USDT
2025-07-16 06:05:50,016 - [KRAKEN] - root - INFO - Swap trade at 2025-07-12 00:00:00+00:00: SUI/USDT -> XRP/USDT
2025-07-16 06:05:50,016 - [KRAKEN] - root - INFO - Total trades: 9 (Entries: 2, Exits: 1, Swaps: 6)
2025-07-16 06:05:50,026 - [KRAKEN] - root - INFO - Strategy execution completed in 0s
2025-07-16 06:05:50,026 - [KRAKEN] - root - INFO - DEBUG: self.elapsed_time = 0.8381922245025635 seconds
2025-07-16 06:05:50,062 - [KRAKEN] - root - INFO - Saved allocation history to allocation_history_1d_1d_with_mtpi_no_rebal_independent_incumbent_2025-02-10.csv
2025-07-16 06:05:50,064 - [KRAKEN] - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-07-16 06:05:50,066 - [KRAKEN] - root - INFO - Assets included in buy-and-hold comparison:
2025-07-16 06:05:50,066 - [KRAKEN] - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-07-16 06:05:50,066 - [KRAKEN] - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-07-16 06:05:50,067 - [KRAKEN] - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-07-16 06:05:50,067 - [KRAKEN] - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-07-16 06:05:50,067 - [KRAKEN] - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-07-16 06:05:50,067 - [KRAKEN] - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-07-16 06:05:50,067 - [KRAKEN] - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-07-16 06:05:50,067 - [KRAKEN] - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-07-16 06:05:50,067 - [KRAKEN] - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-07-16 06:05:50,067 - [KRAKEN] - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-07-16 06:05:50,067 - [KRAKEN] - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-07-16 06:05:50,067 - [KRAKEN] - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-07-16 06:05:50,067 - [KRAKEN] - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-07-16 06:05:50,068 - [KRAKEN] - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-07-16 06:05:50,068 - [KRAKEN] - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-07-16 06:05:50,074 - [KRAKEN] - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:50,081 - [KRAKEN] - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:50,086 - [KRAKEN] - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:50,091 - [KRAKEN] - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:50,095 - [KRAKEN] - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:50,101 - [KRAKEN] - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:50,108 - [KRAKEN] - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:50,114 - [KRAKEN] - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:50,119 - [KRAKEN] - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:50,124 - [KRAKEN] - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:50,131 - [KRAKEN] - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:50,135 - [KRAKEN] - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:50,141 - [KRAKEN] - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:50,151 - [KRAKEN] - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:50,157 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 216 points
2025-07-16 06:05:50,160 - [KRAKEN] - root - INFO - ETH/USDT B&H total return: 17.91%
2025-07-16 06:05:50,167 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 216 points
2025-07-16 06:05:50,168 - [KRAKEN] - root - INFO - BTC/USDT B&H total return: 20.86%
2025-07-16 06:05:50,173 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 216 points
2025-07-16 06:05:50,175 - [KRAKEN] - root - INFO - SOL/USDT B&H total return: -18.20%
2025-07-16 06:05:50,181 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 216 points
2025-07-16 06:05:50,184 - [KRAKEN] - root - INFO - SUI/USDT B&H total return: 27.54%
2025-07-16 06:05:50,189 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 216 points
2025-07-16 06:05:50,193 - [KRAKEN] - root - INFO - XRP/USDT B&H total return: 20.44%
2025-07-16 06:05:50,201 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 216 points
2025-07-16 06:05:50,201 - [KRAKEN] - root - INFO - AAVE/USDT B&H total return: 29.65%
2025-07-16 06:05:50,209 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 216 points
2025-07-16 06:05:50,210 - [KRAKEN] - root - INFO - AVAX/USDT B&H total return: -12.31%
2025-07-16 06:05:50,217 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 216 points
2025-07-16 06:05:50,218 - [KRAKEN] - root - INFO - ADA/USDT B&H total return: 4.84%
2025-07-16 06:05:50,226 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 216 points
2025-07-16 06:05:50,229 - [KRAKEN] - root - INFO - LINK/USDT B&H total return: -14.35%
2025-07-16 06:05:50,234 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 216 points
2025-07-16 06:05:50,235 - [KRAKEN] - root - INFO - TRX/USDT B&H total return: 22.06%
2025-07-16 06:05:50,243 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 216 points
2025-07-16 06:05:50,245 - [KRAKEN] - root - INFO - PEPE/USDT B&H total return: 34.10%
2025-07-16 06:05:50,251 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 216 points
2025-07-16 06:05:50,254 - [KRAKEN] - root - INFO - DOGE/USDT B&H total return: -22.01%
2025-07-16 06:05:50,260 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 216 points
2025-07-16 06:05:50,263 - [KRAKEN] - root - INFO - BNB/USDT B&H total return: 11.52%
2025-07-16 06:05:50,270 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 216 points
2025-07-16 06:05:50,271 - [KRAKEN] - root - INFO - DOT/USDT B&H total return: -15.73%
2025-07-16 06:05:50,279 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:05:50,307 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:50,340 - [KRAKEN] - root - INFO - Using colored segments for single-asset strategy visualization
2025-07-16 06:05:50,741 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:05:50,770 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:56,297 - [KRAKEN] - root - INFO - Added ETH/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:56,298 - [KRAKEN] - root - INFO - Added BTC/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:56,298 - [KRAKEN] - root - INFO - Added SOL/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:56,298 - [KRAKEN] - root - INFO - Added SUI/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:56,298 - [KRAKEN] - root - INFO - Added XRP/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:56,298 - [KRAKEN] - root - INFO - Added AAVE/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:56,298 - [KRAKEN] - root - INFO - Added AVAX/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:56,298 - [KRAKEN] - root - INFO - Added ADA/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:56,298 - [KRAKEN] - root - INFO - Added LINK/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:56,298 - [KRAKEN] - root - INFO - Added TRX/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:56,298 - [KRAKEN] - root - INFO - Added PEPE/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:56,298 - [KRAKEN] - root - INFO - Added DOGE/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:56,298 - [KRAKEN] - root - INFO - Added BNB/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:56,299 - [KRAKEN] - root - INFO - Added DOT/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:56,299 - [KRAKEN] - root - INFO - Added 14 buy-and-hold curves to results
2025-07-16 06:05:56,299 - [KRAKEN] - root - INFO -   - ETH/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:56,299 - [KRAKEN] - root - INFO -   - BTC/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:56,301 - [KRAKEN] - root - INFO -   - SOL/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:56,301 - [KRAKEN] - root - INFO -   - SUI/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:56,302 - [KRAKEN] - root - INFO -   - XRP/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:56,302 - [KRAKEN] - root - INFO -   - AAVE/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:56,302 - [KRAKEN] - root - INFO -   - AVAX/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:56,303 - [KRAKEN] - root - INFO -   - ADA/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:56,304 - [KRAKEN] - root - INFO -   - LINK/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:56,304 - [KRAKEN] - root - INFO -   - TRX/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:56,304 - [KRAKEN] - root - INFO -   - PEPE/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:56,304 - [KRAKEN] - root - INFO -   - DOGE/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:56,305 - [KRAKEN] - root - INFO -   - BNB/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:56,305 - [KRAKEN] - root - INFO -   - DOT/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:56,357 - [KRAKEN] - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-07-16 06:05:56,359 - [KRAKEN] - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-16 06:05:56,364 - [KRAKEN] - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-07-16 06:05:56,364 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:05:56,402 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:56,402 - [KRAKEN] - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 06:05:56,402 - [KRAKEN] - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 06:05:56,402 - [KRAKEN] - root - INFO - Combination method: consensus
2025-07-16 06:05:56,402 - [KRAKEN] - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-16 06:05:56,402 - [KRAKEN] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-16 06:05:56,444 - [KRAKEN] - root - INFO - Loaded 2157 rows of BTC/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:56,446 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:56,446 - [KRAKEN] - root - INFO - Loaded 2157 rows of BTC/USDT data from cache (after filtering).
2025-07-16 06:05:56,447 - [KRAKEN] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-16 06:05:56,447 - [KRAKEN] - root - INFO - Fetched BTC data: 2157 candles from 2019-08-20 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:56,447 - [KRAKEN] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-16 06:05:57,342 - [KRAKEN] - root - INFO - Generated PGO Score signals: {-1: np.int64(996), 0: np.int64(34), 1: np.int64(1127)}
2025-07-16 06:05:57,342 - [KRAKEN] - root - INFO - PGO signal: 1
2025-07-16 06:05:57,342 - [KRAKEN] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-16 06:05:57,617 - [KRAKEN] - root - INFO - Generated BB Score signals: {-1: np.int64(993), 0: np.int64(33), 1: np.int64(1131)}
2025-07-16 06:05:57,617 - [KRAKEN] - root - INFO - Bollinger Bands signal: 1
2025-07-16 06:06:07,331 - [KRAKEN] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-16 06:06:07,334 - [KRAKEN] - root - INFO - DWMA Score signal: 1
2025-07-16 06:06:08,824 - [KRAKEN] - root - INFO - Generated DEMA Supertrend signals
2025-07-16 06:06:08,826 - [KRAKEN] - root - INFO - Signal distribution: {-1: np.int64(1268), 0: np.int64(1), 1: np.int64(888)}
2025-07-16 06:06:08,826 - [KRAKEN] - root - INFO - Generated DEMA Super Score signals
2025-07-16 06:06:08,826 - [KRAKEN] - root - INFO - DEMA Super Score signal: 1
2025-07-16 06:06:11,462 - [KRAKEN] - root - INFO - Generated DPSD signals
2025-07-16 06:06:11,462 - [KRAKEN] - root - INFO - Signal distribution: {-1: np.int64(1087), 0: np.int64(87), 1: np.int64(983)}
2025-07-16 06:06:11,462 - [KRAKEN] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-16 06:06:11,463 - [KRAKEN] - root - INFO - DPSD Score signal: 1
2025-07-16 06:06:11,711 - [KRAKEN] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-16 06:06:11,712 - [KRAKEN] - root - INFO - Generated AAD Score signals using SMA method
2025-07-16 06:06:11,712 - [KRAKEN] - root - INFO - AAD Score signal: 1
2025-07-16 06:06:13,105 - [KRAKEN] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-16 06:06:13,106 - [KRAKEN] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-16 06:06:13,107 - [KRAKEN] - root - INFO - Dynamic EMA Score signal: 1
2025-07-16 06:06:15,438 - [KRAKEN] - root - INFO - Quantile DEMA Score signal: 1
2025-07-16 06:06:15,439 - [KRAKEN] - root - INFO - Individual signals: {'pgo': 1, 'bollinger_bands': 1, 'dwma_score': 1, 'dema_super_score': 1, 'dpsd_score': 1, 'aad_score': 1, 'dynamic_ema_score': 1, 'quantile_dema_score': 1}
2025-07-16 06:06:15,439 - [KRAKEN] - root - INFO - Combined MTPI signal (consensus): 1
2025-07-16 06:06:15,439 - [KRAKEN] - root - INFO - MTPI Score: 1.000000
2025-07-16 06:06:15,439 - [KRAKEN] - root - INFO - Added current MTPI score to results: 1.000000 (using 1d timeframe)
2025-07-16 06:06:15,450 - [KRAKEN] - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-07-16 06:06:15,450 - [KRAKEN] - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 13.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 06:06:15,450 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 13.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 06:06:15,451 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-16 06:06:15,451 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-16 06:06:15,451 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 9.0)
2025-07-16 06:06:15,451 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 3.0)
2025-07-16 06:06:15,451 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 2.0)
2025-07-16 06:06:15,451 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 13.0)
2025-07-16 06:06:15,451 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 12.0)
2025-07-16 06:06:15,453 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 6.0)
2025-07-16 06:06:15,453 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 6.0)
2025-07-16 06:06:15,453 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 10.0)
2025-07-16 06:06:15,454 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 7.0)
2025-07-16 06:06:15,454 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 1.0)
2025-07-16 06:06:15,454 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 9.0)
2025-07-16 06:06:15,454 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 5.0)
2025-07-16 06:06:15,454 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 0.0)
2025-07-16 06:06:15,454 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 4.0)
2025-07-16 06:06:15,454 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 06:06:15,454 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:06:15,454 - [KRAKEN] - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 06:06:15,471 - [KRAKEN] - root - INFO - Saved metrics to new file: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250709_run_20250716_060505.csv
2025-07-16 06:06:15,472 - [KRAKEN] - root - INFO - Saved performance metrics to CSV: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250709_run_20250716_060505.csv
2025-07-16 06:06:15,473 - [KRAKEN] - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-07-16 06:06:15,473 - [KRAKEN] - root - INFO - Results type: <class 'dict'>
2025-07-16 06:06:15,473 - [KRAKEN] - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-07-16 06:06:15,473 - [KRAKEN] - root - INFO - Success flag set to: True
2025-07-16 06:06:15,473 - [KRAKEN] - root - INFO - Message set to: Strategy calculation completed successfully
2025-07-16 06:06:15,473 - [KRAKEN] - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 216 entries
2025-07-16 06:06:15,473 - [KRAKEN] - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-07-16 06:06:15,473 - [KRAKEN] - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 216 entries
2025-07-16 06:06:15,473 - [KRAKEN] - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 276 entries
2025-07-16 06:06:15,473 - [KRAKEN] - root - INFO -   - mtpi_score: <class 'float'>
2025-07-16 06:06:15,473 - [KRAKEN] - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 216 entries
2025-07-16 06:06:15,473 - [KRAKEN] - root - INFO -   - performance_metrics: dict with 3 entries
2025-07-16 06:06:15,474 - [KRAKEN] - root - INFO -   - metrics_file: <class 'str'>
2025-07-16 06:06:15,474 - [KRAKEN] - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-07-16 06:06:15,474 - [KRAKEN] - root - INFO -   - success: <class 'bool'>
2025-07-16 06:06:15,474 - [KRAKEN] - root - INFO -   - message: <class 'str'>
2025-07-16 06:06:15,483 - [KRAKEN] - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-07-16 06:06:15,483 - [KRAKEN] - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: XRP/EUR
2025-07-16 06:06:15,486 - [KRAKEN] - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-07-11 00:00:00+00:00    SUI/EUR
2025-07-12 00:00:00+00:00    XRP/EUR
2025-07-13 00:00:00+00:00    XRP/EUR
2025-07-14 00:00:00+00:00    XRP/EUR
2025-07-15 00:00:00+00:00    XRP/EUR
dtype: object
2025-07-16 06:06:15,487 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 06:06:15,487 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:06:15,487 - [KRAKEN] - root - INFO - [DEBUG] TIE-BREAKING - Strategy: incumbent
2025-07-16 06:06:15,487 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-07-16 06:06:15,487 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 06:06:15,487 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:06:15,487 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 13.0
2025-07-16 06:06:15,487 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 13.0: ['SUI/EUR']
2025-07-16 06:06:15,487 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: SUI/EUR
2025-07-16 06:06:15,487 - [KRAKEN] - root - ERROR - [DEBUG] SELECTED BEST ASSET: SUI/EUR (score: 13.0)
2025-07-16 06:06:15,487 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: SUI/EUR (MTPI signal: 1)
2025-07-16 06:06:15,487 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-07-16 06:06:15,487 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['SUI/EUR']
2025-07-16 06:06:15,487 - [KRAKEN] - root - ERROR - [DEBUG] NO TIE - UPDATING SELECTION: XRP/EUR -> SUI/EUR
2025-07-16 06:06:15,487 - [KRAKEN] - root - ERROR - [DEBUG] NO TIE - Single winner: SUI/EUR
2025-07-16 06:06:15,555 - [KRAKEN] - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-07-16 06:06:15,556 - [KRAKEN] - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:06:15,556 - [KRAKEN] - root - INFO - Single asset strategy with best asset: SUI/EUR
2025-07-16 06:06:15,556 - [KRAKEN] - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-07-16 06:06:15,556 - [KRAKEN] - root - INFO - [DEBUG]   - Best asset selected: SUI/EUR
2025-07-16 06:06:15,556 - [KRAKEN] - root - INFO - [DEBUG]   - Assets held: {'SUI/EUR': 1.0}
2025-07-16 06:06:15,556 - [KRAKEN] - root - INFO - [DEBUG]   - MTPI signal: 1
2025-07-16 06:06:15,556 - [KRAKEN] - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-07-16 06:06:15,556 - [KRAKEN] - root - ERROR - 🚨 ? SUI/EUR WAS SELECTED
2025-07-16 06:06:15,556 - [KRAKEN] - root - INFO - Executing single-asset strategy with best asset: SUI/EUR
2025-07-16 06:06:15,556 - [KRAKEN] - root - INFO - Executing strategy signal: best_asset=SUI/EUR, mtpi_signal=1, mode=live
2025-07-16 06:06:23,612 - [KRAKEN] - root - INFO - Incremented daily trade counter for SUI/EUR: 1/5
2025-07-16 06:06:23,613 - [KRAKEN] - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: SUI/EUR
2025-07-16 06:06:23,613 - [KRAKEN] - root - INFO - Attempting to enter position for SUI/EUR in live mode
2025-07-16 06:06:23,615 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: Starting enter_position attempt
2025-07-16 06:06:23,616 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: Trading mode: live
2025-07-16 06:06:23,618 - [KRAKEN] - root - INFO - TRADE ATTEMPT - SUI/EUR: Getting current market price...
2025-07-16 06:06:23,618 - [KRAKEN] - root - INFO - [DEBUG] PRICE - SUI/EUR: Starting get_current_price
2025-07-16 06:06:23,618 - [KRAKEN] - root - INFO - [DEBUG] PRICE - SUI/EUR: Exchange ID: kraken
2025-07-16 06:06:23,618 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Initializing exchange kraken
2025-07-16 06:06:23,633 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Exchange initialized successfully
2025-07-16 06:06:23,633 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Exchange markets not loaded, loading now...
2025-07-16 06:06:25,502 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Symbol found after loading markets
2025-07-16 06:06:25,503 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Attempting to fetch ticker...
2025-07-16 06:06:25,665 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker fetched successfully
2025-07-16 06:06:25,666 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker data: {'symbol': 'SUI/EUR', 'timestamp': None, 'datetime': None, 'high': 3.5441, 'low': 3.2907, 'bid': 3.4158, 'bidVolume': 469.0, 'ask': 3.417, 'askVolume': 12.0, 'vwap': 3.41597879, 'open': 3.5252, 'close': 3.4119, 'last': 3.4119, 'previousClose': None, 'change': -0.1133, 'percentage': -3.2140020424373086, 'average': 3.4685, 'baseVolume': 599105.71192, 'quoteVolume': 2046532.40488657, 'info': {'a': ['3.41700000', '12', '12.000'], 'b': ['3.41580000', '469', '469.000'], 'c': ['3.********', '7.56491'], 'v': ['96588.73050', '599105.71192'], 'p': ['3.47125354', '3.41597879'], 't': ['294', '2918'], 'l': ['3.********', '3.29070000'], 'h': ['3.52690000', '3.54410000'], 'o': '3.52520000'}, 'indexPrice': None, 'markPrice': None}
2025-07-16 06:06:25,666 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Last price: 3.4119
2025-07-16 06:06:25,666 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: get_current_price returned: 3.4119
2025-07-16 06:06:25,666 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price type: <class 'float'>
2025-07-16 06:06:25,666 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - not price: False
2025-07-16 06:06:25,666 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - price <= 0: False
2025-07-16 06:06:25,666 - [KRAKEN] - root - INFO - TRADE ATTEMPT - SUI/EUR: Current price: 3.********
2025-07-16 06:06:26,639 - [KRAKEN] - root - INFO - Available balance for EUR: 131.********
2025-07-16 06:06:26,639 - [KRAKEN] - root - INFO - Reserved 0.787306 USDC for fees (rate: 0.4% with buffer)
2025-07-16 06:06:26,646 - [KRAKEN] - root - INFO - Loaded market info for 176 trading pairs
2025-07-16 06:06:26,646 - [KRAKEN] - root - INFO - Calculated position size for SUI/EUR: 38.******** (using 99.99% of 131.2308, accounting for fees)
2025-07-16 06:06:26,646 - [KRAKEN] - root - INFO - Calculated position size: 38.******** SUI
2025-07-16 06:06:26,646 - [KRAKEN] - root - INFO - Entering position for SUI/EUR: 38.******** units at 3.******** (value: 129.******** EUR)
2025-07-16 06:06:26,646 - [KRAKEN] - root - INFO - Executing live market buy order for SUI/EUR
2025-07-16 06:06:26,728 - [KRAKEN] - root - INFO - Adjusted base amount for SUI/EUR: 38.******** -> 37.********
2025-07-16 06:06:26,905 - [KRAKEN] - root - WARNING - CCXT NoneType comparison error for SUI/EUR: '>' not supported between instances of 'NoneType' and 'int'
2025-07-16 06:06:26,906 - [KRAKEN] - root - INFO - This is a known Kraken issue - order likely succeeded despite the error
2025-07-16 06:06:26,906 - [KRAKEN] - root - INFO - Created recovery order object for SUI/EUR - trade likely successful
2025-07-16 06:06:26,906 - [KRAKEN] - root - INFO - Filled amount: 38.******** SUI
2025-07-16 06:06:26,906 - [KRAKEN] - root - INFO - Successfully entered position: SUI/EUR, amount: 38.********, price: 3.********
2025-07-16 06:06:26,910 - [KRAKEN] - root - INFO - Trade executed: BUY SUI/EUR, amount=38.********, price=3.********, filled=38.********
2025-07-16 06:06:26,910 - [KRAKEN] - root - INFO - TRADE SUCCESS - SUI/EUR: Successfully updated current asset
2025-07-16 06:06:26,912 - [KRAKEN] - root - INFO - Trade executed: BUY SUI/EUR, amount=38.********, price=3.********, filled=38.********
2025-07-16 06:06:26,913 - [KRAKEN] - root - INFO - Single-asset trade result logged to trade log file
2025-07-16 06:06:26,913 - [KRAKEN] - root - INFO - Trade executed: {'success': True, 'symbol': 'SUI/EUR', 'side': 'buy', 'amount': 38.********000632, 'price': 3.4119, 'order': {'id': 'kraken-SUI/EUR-1752645986', 'symbol': 'SUI/EUR', 'amount': 38.********000632, 'side': 'buy', 'type': 'market', 'status': 'closed', 'filled': 38.********000632, 'cost': 129.********418758, 'average': 3.4119, 'timestamp': 1752645986906, 'datetime': '2025-07-16T06:06:26.906251', 'fee': None, 'trades': [], 'info': {'kraken_nonetype_error_recovery': True}}, 'filled_amount': 38.********000632, 'fee': {}, 'quote_currency': 'EUR', 'base_currency': 'SUI', 'timestamp': '2025-07-16T06:06:26.906859'}
2025-07-16 06:06:27,052 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7000566483:AAEJRUb8vgIBBiC75kBGVhiRMEkoSQ2yRig/sendMessage "HTTP/1.1 200 OK"
2025-07-16 06:06:27,055 - [KRAKEN] - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-07-16 06:06:27,056 - [KRAKEN] - root - INFO - Asset scores (sorted by score):
2025-07-16 06:06:27,056 - [KRAKEN] - root - INFO -   SUI/EUR: score=13.0, status=SELECTED, weight=1.00
2025-07-16 06:06:27,056 - [KRAKEN] - root - INFO -   XRP/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:27,056 - [KRAKEN] - root - INFO -   ADA/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:27,056 - [KRAKEN] - root - INFO -   ETH/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:27,057 - [KRAKEN] - root - INFO -   PEPE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:27,057 - [KRAKEN] - root - INFO -   LINK/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:27,057 - [KRAKEN] - root - INFO -   AAVE/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:27,057 - [KRAKEN] - root - INFO -   AVAX/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:27,057 - [KRAKEN] - root - INFO -   DOGE/EUR: score=5.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:27,057 - [KRAKEN] - root - INFO -   DOT/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:27,057 - [KRAKEN] - root - INFO -   BTC/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:27,057 - [KRAKEN] - root - INFO -   SOL/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:27,057 - [KRAKEN] - root - INFO -   TRX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:27,057 - [KRAKEN] - root - INFO -   BNB/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:27,057 - [KRAKEN] - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-07-16 06:06:27,058 - [KRAKEN] - root - INFO - Extracted asset scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 06:06:27,126 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7000566483:AAEJRUb8vgIBBiC75kBGVhiRMEkoSQ2yRig/sendMessage "HTTP/1.1 200 OK"
2025-07-16 06:06:27,130 - [KRAKEN] - root - INFO - Strategy execution completed successfully in 81.86 seconds
2025-07-16 06:06:27,135 - [KRAKEN] - root - INFO - Saved recovery state to data/state/recovery_state.json
2025-07-16 12:00:49,516 - [KRAKEN] - root - INFO - [DEBUG] PRICE - SUI/EUR: Starting get_current_price
2025-07-16 12:00:49,518 - [KRAKEN] - root - INFO - [DEBUG] PRICE - SUI/EUR: Exchange ID: kraken
2025-07-16 12:00:49,518 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Using existing exchange instance
2025-07-16 12:00:49,518 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Symbol found in exchange markets
2025-07-16 12:00:49,518 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Attempting to fetch ticker...
2025-07-16 12:00:49,622 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker fetched successfully
2025-07-16 12:00:49,623 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker data: {'symbol': 'SUI/EUR', 'timestamp': None, 'datetime': None, 'high': 3.5441, 'low': 3.3333, 'bid': 3.4539, 'bidVolume': 2868.0, 'ask': 3.454, 'askVolume': 805.0, 'vwap': 3.44984081, 'open': 3.5252, 'close': 3.454, 'last': 3.454, 'previousClose': None, 'change': -0.0712, 'percentage': -2.019743560649041, 'average': 3.4896, 'baseVolume': 501612.05327, 'quoteVolume': 1730481.73215874, 'info': {'a': ['3.45400000', '805', '805.000'], 'b': ['3.45390000', '2868', '2868.000'], 'c': ['3.45400000', '16.20766'], 'v': ['188484.77120', '501612.05327'], 'p': ['3.46756514', '3.44984081'], 't': ['838', '2575'], 'l': ['3.********', '3.33330000'], 'h': ['3.52690000', '3.54410000'], 'o': '3.52520000'}, 'indexPrice': None, 'markPrice': None}
2025-07-16 12:00:49,623 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Last price: 3.454
2025-07-16 12:00:49,711 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7000566483:AAEJRUb8vgIBBiC75kBGVhiRMEkoSQ2yRig/sendMessage "HTTP/1.1 200 OK"
2025-07-16 12:00:49,724 - [KRAKEN] - root - INFO - Status update sent
