2025-07-16 00:03:32,843 - [BITVA<PERSON>] - root - INFO - Added SUI/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:32,844 - [BITVA<PERSON>] - root - INFO - Added XRP/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:32,844 - [B<PERSON><PERSON><PERSON>] - root - INFO - Added AAVE/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:32,844 - [BITVAVO] - root - INFO - Added AVAX/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:32,844 - [BITVAVO] - root - INFO - Added ADA/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:32,844 - [BIT<PERSON><PERSON>] - root - INFO - Added LINK/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:32,844 - [B<PERSON><PERSON><PERSON>] - root - INFO - Added TRX/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:32,844 - [BITVA<PERSON>] - root - INFO - Added PEPE/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:32,844 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - Added DOGE/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:32,844 - [BITVAVO] - root - INFO - Added BNB/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:32,844 - [BITVAVO] - root - INFO - Added DOT/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:32,844 - [BITVAVO] - root - INFO - Added 14 buy-and-hold curves to results
2025-07-16 00:03:32,845 - [BITVAVO] - root - INFO -   - ETH/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:32,845 - [BITVAVO] - root - INFO -   - BTC/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:32,845 - [BITVAVO] - root - INFO -   - SOL/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:32,845 - [BITVAVO] - root - INFO -   - SUI/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:32,845 - [BITVAVO] - root - INFO -   - XRP/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:32,845 - [BITVAVO] - root - INFO -   - AAVE/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:32,846 - [BITVAVO] - root - INFO -   - AVAX/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:32,846 - [BITVAVO] - root - INFO -   - ADA/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:32,846 - [BITVAVO] - root - INFO -   - LINK/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:32,846 - [BITVAVO] - root - INFO -   - TRX/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:32,846 - [BITVAVO] - root - INFO -   - PEPE/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:32,846 - [BITVAVO] - root - INFO -   - DOGE/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:32,846 - [BITVAVO] - root - INFO -   - BNB/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:32,847 - [BITVAVO] - root - INFO -   - DOT/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:32,869 - [BITVAVO] - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-07-16 00:03:32,869 - [BITVAVO] - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-16 00:03:32,871 - [BITVAVO] - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-07-16 00:03:32,871 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 00:03:32,886 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-16 00:03:32,886 - [BITVAVO] - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 00:03:32,886 - [BITVAVO] - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 00:03:32,886 - [BITVAVO] - root - INFO - Combination method: consensus
2025-07-16 00:03:32,886 - [BITVAVO] - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-16 00:03:32,886 - [BITVAVO] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-16 00:03:32,907 - [BITVAVO] - root - INFO - Loaded 2157 rows of BTC/USDT data from cache (last updated: 2025-07-16)
2025-07-16 00:03:32,908 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 00:03:32,908 - [BITVAVO] - root - INFO - Loaded 2157 rows of BTC/USDT data from cache (after filtering).
2025-07-16 00:03:32,908 - [BITVAVO] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-16 00:03:32,909 - [BITVAVO] - root - INFO - Fetched BTC data: 2157 candles from 2019-08-20 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:32,909 - [BITVAVO] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-16 00:03:33,252 - [BITVAVO] - root - INFO - Generated PGO Score signals: {-1: np.int64(996), 0: np.int64(34), 1: np.int64(1127)}
2025-07-16 00:03:33,253 - [BITVAVO] - root - INFO - PGO signal: 1
2025-07-16 00:03:33,253 - [BITVAVO] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-16 00:03:33,363 - [BITVAVO] - root - INFO - Generated BB Score signals: {-1: np.int64(993), 0: np.int64(33), 1: np.int64(1131)}
2025-07-16 00:03:33,363 - [BITVAVO] - root - INFO - Bollinger Bands signal: 1
2025-07-16 00:03:35,708 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:03:39,120 - [BITVAVO] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-16 00:03:39,121 - [BITVAVO] - root - INFO - DWMA Score signal: 1
2025-07-16 00:03:39,719 - [BITVAVO] - root - INFO - Generated DEMA Supertrend signals
2025-07-16 00:03:39,720 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1268), 0: np.int64(1), 1: np.int64(888)}
2025-07-16 00:03:39,720 - [BITVAVO] - root - INFO - Generated DEMA Super Score signals
2025-07-16 00:03:39,720 - [BITVAVO] - root - INFO - DEMA Super Score signal: 1
2025-07-16 00:03:41,361 - [BITVAVO] - root - INFO - Generated DPSD signals
2025-07-16 00:03:41,361 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1087), 0: np.int64(87), 1: np.int64(983)}
2025-07-16 00:03:41,362 - [BITVAVO] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-16 00:03:41,362 - [BITVAVO] - root - INFO - DPSD Score signal: 1
2025-07-16 00:03:41,465 - [BITVAVO] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-16 00:03:41,465 - [BITVAVO] - root - INFO - Generated AAD Score signals using SMA method
2025-07-16 00:03:41,465 - [BITVAVO] - root - INFO - AAD Score signal: 1
2025-07-16 00:03:42,226 - [BITVAVO] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-16 00:03:42,226 - [BITVAVO] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-16 00:03:42,227 - [BITVAVO] - root - INFO - Dynamic EMA Score signal: 1
2025-07-16 00:03:43,762 - [BITVAVO] - root - INFO - Quantile DEMA Score signal: 1
2025-07-16 00:03:43,763 - [BITVAVO] - root - INFO - Individual signals: {'pgo': 1, 'bollinger_bands': 1, 'dwma_score': 1, 'dema_super_score': 1, 'dpsd_score': 1, 'aad_score': 1, 'dynamic_ema_score': 1, 'quantile_dema_score': 1}
2025-07-16 00:03:43,763 - [BITVAVO] - root - INFO - Combined MTPI signal (consensus): 1
2025-07-16 00:03:43,763 - [BITVAVO] - root - INFO - MTPI Score: 1.000000
2025-07-16 00:03:43,763 - [BITVAVO] - root - INFO - Added current MTPI score to results: 1.000000 (using 1d timeframe)
2025-07-16 00:03:43,768 - [BITVAVO] - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-07-16 00:03:43,768 - [BITVAVO] - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 13.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 00:03:43,768 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 13.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 00:03:43,769 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-16 00:03:43,769 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-16 00:03:43,769 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 9.0)
2025-07-16 00:03:43,769 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 3.0)
2025-07-16 00:03:43,769 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 2.0)
2025-07-16 00:03:43,769 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 13.0)
2025-07-16 00:03:43,769 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 12.0)
2025-07-16 00:03:43,769 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 6.0)
2025-07-16 00:03:43,769 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 6.0)
2025-07-16 00:03:43,769 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 10.0)
2025-07-16 00:03:43,769 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 7.0)
2025-07-16 00:03:43,769 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 1.0)
2025-07-16 00:03:43,769 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 9.0)
2025-07-16 00:03:43,769 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 5.0)
2025-07-16 00:03:43,770 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 0.0)
2025-07-16 00:03:43,770 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 4.0)
2025-07-16 00:03:43,770 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 00:03:43,770 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 00:03:43,770 - [BITVAVO] - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 00:03:43,793 - [BITVAVO] - root - INFO - Appended metrics to existing file: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250709_run_20250712_085257.csv
2025-07-16 00:03:43,793 - [BITVAVO] - root - INFO - Saved performance metrics to CSV: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250709_run_20250712_085257.csv
2025-07-16 00:03:43,794 - [BITVAVO] - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-07-16 00:03:43,794 - [BITVAVO] - root - INFO - Results type: <class 'dict'>
2025-07-16 00:03:43,794 - [BITVAVO] - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-07-16 00:03:43,794 - [BITVAVO] - root - INFO - Success flag set to: True
2025-07-16 00:03:43,794 - [BITVAVO] - root - INFO - Message set to: Strategy calculation completed successfully
2025-07-16 00:03:43,794 - [BITVAVO] - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 216 entries
2025-07-16 00:03:43,794 - [BITVAVO] - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-07-16 00:03:43,794 - [BITVAVO] - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 216 entries
2025-07-16 00:03:43,794 - [BITVAVO] - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 276 entries
2025-07-16 00:03:43,794 - [BITVAVO] - root - INFO -   - mtpi_score: <class 'float'>
2025-07-16 00:03:43,794 - [BITVAVO] - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 216 entries
2025-07-16 00:03:43,794 - [BITVAVO] - root - INFO -   - performance_metrics: dict with 3 entries
2025-07-16 00:03:43,795 - [BITVAVO] - root - INFO -   - metrics_file: <class 'str'>
2025-07-16 00:03:43,795 - [BITVAVO] - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-07-16 00:03:43,795 - [BITVAVO] - root - INFO -   - success: <class 'bool'>
2025-07-16 00:03:43,795 - [BITVAVO] - root - INFO -   - message: <class 'str'>
2025-07-16 00:03:43,795 - [BITVAVO] - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-07-16 00:03:43,795 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: XRP/EUR
2025-07-16 00:03:43,796 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-07-11 00:00:00+00:00    SUI/EUR
2025-07-12 00:00:00+00:00    XRP/EUR
2025-07-13 00:00:00+00:00    XRP/EUR
2025-07-14 00:00:00+00:00    XRP/EUR
2025-07-15 00:00:00+00:00    XRP/EUR
dtype: object
2025-07-16 00:03:43,796 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 00:03:43,797 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 00:03:43,797 - [BITVAVO] - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-07-16 00:03:43,797 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-07-16 00:03:43,797 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 00:03:43,797 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 00:03:43,797 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 13.0
2025-07-16 00:03:43,797 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 13.0: ['SUI/EUR']
2025-07-16 00:03:43,797 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: SUI/EUR
2025-07-16 00:03:43,797 - [BITVAVO] - root - ERROR - [DEBUG] SELECTED BEST ASSET: SUI/EUR (score: 13.0)
2025-07-16 00:03:43,797 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: SUI/EUR (MTPI signal: 1)
2025-07-16 00:03:43,797 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-07-16 00:03:43,797 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['SUI/EUR']
2025-07-16 00:03:43,798 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - UPDATING SELECTION: XRP/EUR -> SUI/EUR
2025-07-16 00:03:43,798 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - Single winner: SUI/EUR
2025-07-16 00:03:43,827 - [BITVAVO] - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-07-16 00:03:43,828 - [BITVAVO] - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 00:03:43,828 - [BITVAVO] - root - INFO - Single asset strategy with best asset: SUI/EUR
2025-07-16 00:03:43,828 - [BITVAVO] - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-07-16 00:03:43,828 - [BITVAVO] - root - INFO - [DEBUG]   - Best asset selected: SUI/EUR
2025-07-16 00:03:43,828 - [BITVAVO] - root - INFO - [DEBUG]   - Assets held: {'SUI/EUR': 1.0}
2025-07-16 00:03:43,828 - [BITVAVO] - root - INFO - [DEBUG]   - MTPI signal: 1
2025-07-16 00:03:43,828 - [BITVAVO] - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-07-16 00:03:43,828 - [BITVAVO] - root - ERROR - 🚨 ? SUI/EUR WAS SELECTED
2025-07-16 00:03:43,946 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-16 00:03:43,948 - [BITVAVO] - root - INFO - Executing single-asset strategy with best asset: SUI/EUR
2025-07-16 00:03:43,949 - [BITVAVO] - root - INFO - Executing strategy signal: best_asset=SUI/EUR, mtpi_signal=1, mode=live
2025-07-16 00:03:43,949 - [BITVAVO] - root - INFO - Already holding the best asset: SUI/EUR. No trade needed.
2025-07-16 00:03:43,954 - [BITVAVO] - root - INFO - Trade executed: HOLD SUI/EUR, amount=0.00000000, price=0.00000000, filled=0.00000000
2025-07-16 00:03:43,959 - [BITVAVO] - root - INFO - Trade executed: UNKNOWN SUI/EUR, amount=0.00000000, price=0.00000000, filled=0.00000000
2025-07-16 00:03:43,960 - [BITVAVO] - root - INFO - Single-asset trade result logged to trade log file
2025-07-16 00:03:43,960 - [BITVAVO] - root - INFO - Trade executed: {'success': True, 'reason': 'Already holding best asset', 'symbol': 'SUI/EUR'}
2025-07-16 00:03:43,965 - [BITVAVO] - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-07-16 00:03:43,965 - [BITVAVO] - root - INFO - Asset scores (sorted by score):
2025-07-16 00:03:43,965 - [BITVAVO] - root - INFO -   SUI/EUR: score=13.0, status=SELECTED, weight=1.00
2025-07-16 00:03:43,965 - [BITVAVO] - root - INFO -   XRP/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:03:43,965 - [BITVAVO] - root - INFO -   ADA/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:03:43,965 - [BITVAVO] - root - INFO -   ETH/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:03:43,965 - [BITVAVO] - root - INFO -   PEPE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:03:43,965 - [BITVAVO] - root - INFO -   LINK/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:03:43,965 - [BITVAVO] - root - INFO -   AAVE/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:03:43,965 - [BITVAVO] - root - INFO -   AVAX/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:03:43,965 - [BITVAVO] - root - INFO -   DOGE/EUR: score=5.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:03:43,965 - [BITVAVO] - root - INFO -   DOT/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:03:43,965 - [BITVAVO] - root - INFO -   BTC/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:03:43,966 - [BITVAVO] - root - INFO -   SOL/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:03:43,966 - [BITVAVO] - root - INFO -   TRX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:03:43,966 - [BITVAVO] - root - INFO -   BNB/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:03:43,966 - [BITVAVO] - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-07-16 00:03:43,966 - [BITVAVO] - root - INFO - Extracted asset scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 00:03:44,027 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-16 00:03:44,029 - [BITVAVO] - root - INFO - Strategy execution completed successfully in 52.62 seconds
2025-07-16 00:03:44,033 - [BITVAVO] - root - INFO - Saved recovery state to data/state/recovery_state.json
2025-07-16 00:03:44,034 - [BITVAVO] - root - WARNING - Recovery from strategy_execution_failure failure was unsuccessful
2025-07-16 00:03:44,046 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 400 Bad Request"
2025-07-16 00:03:44,047 - [BITVAVO] - root - WARNING - Failed to send with Markdown formatting: Can't parse entities: can't find end of the entity starting at byte offset 138
2025-07-16 00:03:44,081 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
2025-07-16 00:03:46,145 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:03:56,437 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:04:06,731 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:04:17,030 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:04:27,323 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:04:37,599 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:04:47,871 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:04:58,159 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:05:08,455 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:05:18,751 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:05:29,049 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:05:39,338 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:05:49,621 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:05:59,912 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:06:10,207 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:06:20,497 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:06:30,783 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:06:41,072 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:06:44,085 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:06:51,359 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:07:01,652 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:07:11,942 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:07:22,231 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:07:32,525 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:07:42,821 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:07:53,107 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:08:03,395 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:08:13,685 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:08:23,974 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:08:34,262 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:08:44,556 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:08:54,844 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:09:05,134 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:09:15,427 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:09:26,220 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:09:36,515 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:09:46,807 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:09:57,099 - [BITVAVO] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 06:04:34,488 - [BITVAVO] - root - INFO - Received signal 15, shutting down...
2025-07-16 06:04:41,210 - [BITVAVO] - root - INFO - Network watchdog stopped
2025-07-16 06:04:41,210 - [BITVAVO] - root - INFO - Network watchdog stopped
2025-07-16 06:04:41,211 - [BITVAVO] - root - INFO - Background service stopped
2025-07-16 06:04:41,294 - [BITVAVO] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7684436013:AAF3A0h8zCSBVO9ddZmO9Sqd_h96s7srfwU/sendMessage "HTTP/1.1 200 OK"
