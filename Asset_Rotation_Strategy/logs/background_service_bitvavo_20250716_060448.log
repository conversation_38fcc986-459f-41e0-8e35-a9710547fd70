2025-07-16 06:04:48,676 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - Exchange-specific logging initialized for bitvavo
2025-07-16 06:04:49,259 - [B<PERSON>VA<PERSON>] - root - INFO - Telegram command handlers registered
2025-07-16 06:04:49,261 - [<PERSON><PERSON><PERSON><PERSON>] - root - ERROR - Error in Telegram polling: set_wakeup_fd only works in main thread of the main interpreter
2025-07-16 06:04:49,261 - [BITVAVO] - root - WARNING - Continuing without Telegram bot polling. Notifications will still be sent.
2025-07-16 06:04:49,262 - [BITVAVO] - root - INFO - Telegram bot polling started
2025-07-16 06:04:49,262 - [BITVAVO] - root - INFO - Telegram notifier initialized with notification level: standard
2025-07-16 06:04:49,262 - [BITVAVO] - root - INFO - Telegram notification channel initialized
2025-07-16 06:04:49,264 - [BITVAVO] - root - INFO - Successfully loaded templates using utf-8 encoding
2025-07-16 06:04:49,265 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - <PERSON>aded 26 templates from file
2025-07-16 06:04:49,265 - [B<PERSON><PERSON><PERSON>] - root - INFO - Notification manager initialized with 1 channels
2025-07-16 06:04:49,265 - [BITVAVO] - root - INFO - Notification manager initialized
2025-07-16 06:04:49,265 - [BITVAVO] - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-07-16 06:04:49,265 - [BITVAVO] - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-07-16 06:04:49,265 - [BITVAVO] - root - INFO - Set up critical time windows for 1d timeframe
2025-07-16 06:04:49,265 - [BITVAVO] - root - INFO - Network watchdog initialized with 10s check interval
2025-07-16 06:04:49,266 - [BITVAVO] - root - INFO - Loaded recovery state from data/state/recovery_state.json
2025-07-16 06:04:49,267 - [BITVAVO] - root - INFO - No state file found at /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/data/state/data/state/background_service_20250627_214259.json.json
2025-07-16 06:04:49,267 - [BITVAVO] - root - INFO - Recovery manager initialized
2025-07-16 06:04:49,267 - [BITVAVO] - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: bitvavo
2025-07-16 06:04:49,268 - [BITVAVO] - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'bitvavo', 'exchange_params': {'bitvavo': {'operator_id': 1001}}, 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'default': 5.0}, 'mode': 'live', 'order_type': 'market', 'position_size_pct': 99.99, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 20}, 'transaction_fee_rate': 0.0025}
2025-07-16 06:04:49,269 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-16 06:04:49,312 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-16 06:04:49,312 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-16 06:04:49,312 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-16 06:04:49,313 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-16 06:04:49,313 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-16 06:04:49,313 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-16 06:04:49,313 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-16 06:04:49,313 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-16 06:04:49,313 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-16 06:04:49,313 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-16 06:04:49,340 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-16 06:04:50,002 - [BITVAVO] - root - INFO - Successfully loaded markets for bitvavo.
2025-07-16 06:04:50,074 - [BITVAVO] - root - INFO - Successfully connected to bitvavo exchange.
2025-07-16 06:04:50,076 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-16 06:04:50,076 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-16 06:04:50,076 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-16 06:04:50,077 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-16 06:04:50,077 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-16 06:04:50,103 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-16 06:04:51,161 - [BITVAVO] - root - INFO - Successfully connected to bitvavo exchange.
2025-07-16 06:04:51,161 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-16 06:04:51,190 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-16 06:04:51,190 - [BITVAVO] - root - INFO - Trading executor initialized for bitvavo
2025-07-16 06:04:51,190 - [BITVAVO] - root - INFO - Trading mode: live
2025-07-16 06:04:51,190 - [BITVAVO] - root - INFO - Trading enabled: True
2025-07-16 06:04:51,190 - [BITVAVO] - root - INFO - Getting credentials for exchange_id: bitvavo
2025-07-16 06:04:51,190 - [BITVAVO] - root - INFO - Looking for environment variables: BITVAVO_API_KEY, BITVAVO_API_SECRET
2025-07-16 06:04:51,192 - [BITVAVO] - root - INFO - Loaded API key from environment variable BITVAVO_API_KEY
2025-07-16 06:04:51,192 - [BITVAVO] - root - INFO - Loaded API secret from environment variable BITVAVO_API_SECRET
2025-07-16 06:04:51,192 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-16 06:04:51,218 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-16 06:04:51,888 - [BITVAVO] - root - INFO - Successfully loaded markets for bitvavo.
2025-07-16 06:04:51,960 - [BITVAVO] - root - INFO - Successfully connected to bitvavo exchange.
2025-07-16 06:04:51,963 - [BITVAVO] - root - INFO - Notification manager passed to trading executor
2025-07-16 06:04:51,963 - [BITVAVO] - root - INFO - Trading enabled in live mode
2025-07-16 06:04:52,261 - [BITVAVO] - root - INFO - Connected to bitvavo, balance: 95.51 EUR
2025-07-16 06:04:52,262 - [BITVAVO] - root - INFO - Generated run ID: 20250716_060452
2025-07-16 06:04:52,262 - [BITVAVO] - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-07-16 06:04:52,262 - [BITVAVO] - root - INFO - Background service initialized
2025-07-16 06:04:52,262 - [BITVAVO] - root - INFO - Network watchdog started
2025-07-16 06:04:52,264 - [BITVAVO] - root - INFO - Network watchdog started
2025-07-16 06:04:52,265 - [BITVAVO] - root - INFO - Schedule set up for 1d timeframe
2025-07-16 06:04:52,267 - [BITVAVO] - root - INFO - Executing strategy (run #1)...
2025-07-16 06:04:52,268 - [BITVAVO] - root - INFO - Background service started
2025-07-16 06:04:52,273 - [BITVAVO] - root - INFO - Resetting daily trade counters for this strategy execution
2025-07-16 06:04:52,273 - [BITVAVO] - root - INFO - No trades recorded today (Max: 5)
2025-07-16 06:04:52,274 - [BITVAVO] - root - INFO - Initialized daily trades counter for 2025-07-16
2025-07-16 06:04:52,274 - [BITVAVO] - root - INFO - Creating snapshot for candle timestamp: 20250716
2025-07-16 06:04:52,331 - [BITVAVO] - root - WARNING - Failed to send with Markdown formatting: Unknown error in HTTP implementation: AttributeError("'AsyncEvent' object has no attribute '_anyio_event'")
2025-07-16 06:04:53,335 - [BITVAVO] - root - ERROR - Failed to send notification: Pool timeout: All connections in the connection pool are occupied. Request was *not* sent to Telegram. Consider adjusting the connection pool size or the pool timeout.
2025-07-16 06:04:53,335 - [BITVAVO] - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-07-16 06:04:53,335 - [BITVAVO] - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-07-16 06:04:53,335 - [BITVAVO] - root - INFO - Using recent date for performance tracking: 2025-07-09
2025-07-16 06:04:53,336 - [BITVAVO] - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-07-16 06:04:53,338 - [BITVAVO] - root - WARNING - Failed to send with Markdown formatting: Pool timeout: All connections in the connection pool are occupied. Request was *not* sent to Telegram. Consider adjusting the connection pool size or the pool timeout.
2025-07-16 06:04:53,456 - [BITVAVO] - root - INFO - Loaded 2157 rows of ETH/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:53,458 - [BITVAVO] - root - INFO - Last timestamp in cache for ETH/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,461 - [BITVAVO] - root - INFO - Expected last timestamp for ETH/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,461 - [BITVAVO] - root - INFO - Data is up to date for ETH/USDT
2025-07-16 06:04:53,465 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:53,519 - [BITVAVO] - root - INFO - Loaded 2157 rows of BTC/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:53,521 - [BITVAVO] - root - INFO - Last timestamp in cache for BTC/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,522 - [BITVAVO] - root - INFO - Expected last timestamp for BTC/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,522 - [BITVAVO] - root - INFO - Data is up to date for BTC/USDT
2025-07-16 06:04:53,524 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:53,575 - [BITVAVO] - root - INFO - Loaded 1800 rows of SOL/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:53,576 - [BITVAVO] - root - INFO - Last timestamp in cache for SOL/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,576 - [BITVAVO] - root - INFO - Expected last timestamp for SOL/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,576 - [BITVAVO] - root - INFO - Data is up to date for SOL/USDT
2025-07-16 06:04:53,581 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:53,613 - [BITVAVO] - root - INFO - Loaded 805 rows of SUI/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:53,614 - [BITVAVO] - root - INFO - Last timestamp in cache for SUI/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,614 - [BITVAVO] - root - INFO - Expected last timestamp for SUI/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,614 - [BITVAVO] - root - INFO - Data is up to date for SUI/USDT
2025-07-16 06:04:53,615 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:53,671 - [BITVAVO] - root - INFO - Loaded 2157 rows of XRP/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:53,671 - [BITVAVO] - root - INFO - Last timestamp in cache for XRP/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,672 - [BITVAVO] - root - INFO - Expected last timestamp for XRP/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,672 - [BITVAVO] - root - INFO - Data is up to date for XRP/USDT
2025-07-16 06:04:53,675 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:53,721 - [BITVAVO] - root - INFO - Loaded 1735 rows of AAVE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:53,723 - [BITVAVO] - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,724 - [BITVAVO] - root - INFO - Expected last timestamp for AAVE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,724 - [BITVAVO] - root - INFO - Data is up to date for AAVE/USDT
2025-07-16 06:04:53,727 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:53,776 - [BITVAVO] - root - INFO - Loaded 1758 rows of AVAX/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:53,776 - [BITVAVO] - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,777 - [BITVAVO] - root - INFO - Expected last timestamp for AVAX/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,777 - [BITVAVO] - root - INFO - Data is up to date for AVAX/USDT
2025-07-16 06:04:53,781 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:53,836 - [BITVAVO] - root - INFO - Loaded 2157 rows of ADA/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:53,837 - [BITVAVO] - root - INFO - Last timestamp in cache for ADA/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,840 - [BITVAVO] - root - INFO - Expected last timestamp for ADA/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,840 - [BITVAVO] - root - INFO - Data is up to date for ADA/USDT
2025-07-16 06:04:53,843 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:53,898 - [BITVAVO] - root - INFO - Loaded 2157 rows of LINK/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:53,900 - [BITVAVO] - root - INFO - Last timestamp in cache for LINK/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,901 - [BITVAVO] - root - INFO - Expected last timestamp for LINK/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,901 - [BITVAVO] - root - INFO - Data is up to date for LINK/USDT
2025-07-16 06:04:53,905 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:53,960 - [BITVAVO] - root - INFO - Loaded 2157 rows of TRX/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:53,961 - [BITVAVO] - root - INFO - Last timestamp in cache for TRX/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,965 - [BITVAVO] - root - INFO - Expected last timestamp for TRX/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:53,965 - [BITVAVO] - root - INFO - Data is up to date for TRX/USDT
2025-07-16 06:04:53,971 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:54,001 - [BITVAVO] - root - INFO - Loaded 803 rows of PEPE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:54,001 - [BITVAVO] - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:54,004 - [BITVAVO] - root - INFO - Expected last timestamp for PEPE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:54,004 - [BITVAVO] - root - INFO - Data is up to date for PEPE/USDT
2025-07-16 06:04:54,005 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:54,048 - [BITVAVO] - root - INFO - Loaded 2157 rows of DOGE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:54,049 - [BITVAVO] - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:54,049 - [BITVAVO] - root - INFO - Expected last timestamp for DOGE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:54,049 - [BITVAVO] - root - INFO - Data is up to date for DOGE/USDT
2025-07-16 06:04:54,051 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:54,088 - [BITVAVO] - root - INFO - Loaded 2157 rows of BNB/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:54,089 - [BITVAVO] - root - INFO - Last timestamp in cache for BNB/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:54,089 - [BITVAVO] - root - INFO - Expected last timestamp for BNB/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:54,089 - [BITVAVO] - root - INFO - Data is up to date for BNB/USDT
2025-07-16 06:04:54,093 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:54,154 - [BITVAVO] - root - INFO - Loaded 1793 rows of DOT/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:54,155 - [BITVAVO] - root - INFO - Last timestamp in cache for DOT/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:54,155 - [BITVAVO] - root - INFO - Expected last timestamp for DOT/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:04:54,155 - [BITVAVO] - root - INFO - Data is up to date for DOT/USDT
2025-07-16 06:04:54,161 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:54,166 - [BITVAVO] - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-07-16 06:04:54,166 - [BITVAVO] - root - INFO - MTPI Multi-Indicator Configuration:
2025-07-16 06:04:54,167 - [BITVAVO] - root - INFO -   - Number of indicators: 8
2025-07-16 06:04:54,167 - [BITVAVO] - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 06:04:54,167 - [BITVAVO] - root - INFO -   - Combination method: consensus
2025-07-16 06:04:54,167 - [BITVAVO] - root - INFO -   - Long threshold: 0.1
2025-07-16 06:04:54,167 - [BITVAVO] - root - INFO -   - Short threshold: -0.1
2025-07-16 06:04:54,167 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-16 06:04:54,167 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:04:54,167 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-07-16 06:04:54,167 - [BITVAVO] - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-07-16 06:04:54,167 - [BITVAVO] - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-07-16 06:04:54,167 - [BITVAVO] - root - INFO - Parameters: use_mtpi_signal=True, mtpi_indicator_type=PGO
2025-07-16 06:04:54,168 - [BITVAVO] - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-07-16 06:04:54,168 - [BITVAVO] - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-07-16 06:04:54,168 - [BITVAVO] - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-07-16 06:04:54,168 - [BITVAVO] - root - INFO - n_assets=1, use_weighted_allocation=False
2025-07-16 06:04:54,168 - [BITVAVO] - root - INFO - Using provided trend method: PGO For Loop
2025-07-16 06:04:54,168 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-16 06:04:54,191 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-16 06:04:54,193 - [BITVAVO] - root - INFO - Saving configuration to config/settings_bitvavo_eur.yaml...
2025-07-16 06:04:54,207 - [BITVAVO] - root - INFO - Configuration saved successfully.
2025-07-16 06:04:54,208 - [BITVAVO] - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-16 06:04:54,208 - [BITVAVO] - root - INFO - Number of trend detection assets: 14
2025-07-16 06:04:54,208 - [BITVAVO] - root - INFO - Selected assets type: <class 'list'>
2025-07-16 06:04:54,208 - [BITVAVO] - root - INFO - Trading execution assets (EUR): ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:04:54,208 - [BITVAVO] - root - INFO - Number of trading assets: 14
2025-07-16 06:04:54,208 - [BITVAVO] - root - INFO - Trading assets type: <class 'list'>
2025-07-16 06:04:54,343 - [BITVAVO] - root - ERROR - Failed to send notification: Pool timeout: All connections in the connection pool are occupied. Request was *not* sent to Telegram. Consider adjusting the connection pool size or the pool timeout.
2025-07-16 06:04:54,523 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:04:54,544 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-16 06:04:54,567 - [BITVAVO] - root - INFO - Loading configuration from config/settings_bitvavo_eur.yaml...
2025-07-16 06:04:54,587 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-16 06:04:54,588 - [BITVAVO] - root - INFO - Execution context: backtesting
2025-07-16 06:04:54,588 - [BITVAVO] - root - INFO - Execution timing: candle_close
2025-07-16 06:04:54,588 - [BITVAVO] - root - INFO - Ratio calculation method: independent
2025-07-16 06:04:54,588 - [BITVAVO] - root - INFO - Tie-breaking strategy: imcumbent
2025-07-16 06:04:54,588 - [BITVAVO] - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-07-16 06:04:54,588 - [BITVAVO] - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 06:04:54,588 - [BITVAVO] - root - INFO - MTPI combination method override: consensus
2025-07-16 06:04:54,588 - [BITVAVO] - root - INFO - MTPI long threshold override: 0.1
2025-07-16 06:04:54,588 - [BITVAVO] - root - INFO - MTPI short threshold override: -0.1
2025-07-16 06:04:54,588 - [BITVAVO] - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-07-16 06:04:54,588 - [BITVAVO] - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-07-16 06:04:54,590 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:04:54,591 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:04:54,591 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:04:54,591 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:04:54,591 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:04:54,592 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:04:54,592 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:04:54,592 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:04:54,592 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:04:54,593 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:04:54,593 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:04:54,593 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:04:54,593 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:04:54,594 - [BITVAVO] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:04:54,594 - [BITVAVO] - root - INFO - Checking cache for 14 symbols (1d)...
2025-07-16 06:04:54,626 - [BITVAVO] - root - INFO - Loaded 216 rows of ETH/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:54,627 - [BITVAVO] - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:04:54,627 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:54,628 - [BITVAVO] - root - INFO - Loaded 216 rows of ETH/USDT data from cache (after filtering).
2025-07-16 06:04:54,676 - [BITVAVO] - root - INFO - Loaded 216 rows of BTC/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:54,677 - [BITVAVO] - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:04:54,680 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:54,680 - [BITVAVO] - root - INFO - Loaded 216 rows of BTC/USDT data from cache (after filtering).
2025-07-16 06:04:54,740 - [BITVAVO] - root - INFO - Loaded 216 rows of SOL/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:54,744 - [BITVAVO] - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:04:54,744 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:54,745 - [BITVAVO] - root - INFO - Loaded 216 rows of SOL/USDT data from cache (after filtering).
2025-07-16 06:04:54,772 - [BITVAVO] - root - INFO - Loaded 216 rows of SUI/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:54,774 - [BITVAVO] - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:04:54,774 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:54,775 - [BITVAVO] - root - INFO - Loaded 216 rows of SUI/USDT data from cache (after filtering).
2025-07-16 06:04:54,805 - [BITVAVO] - root - INFO - Loaded 216 rows of XRP/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:54,806 - [BITVAVO] - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:04:54,807 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:54,807 - [BITVAVO] - root - INFO - Loaded 216 rows of XRP/USDT data from cache (after filtering).
2025-07-16 06:04:54,834 - [BITVAVO] - root - INFO - Loaded 216 rows of AAVE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:54,835 - [BITVAVO] - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:04:54,836 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:54,836 - [BITVAVO] - root - INFO - Loaded 216 rows of AAVE/USDT data from cache (after filtering).
2025-07-16 06:04:54,864 - [BITVAVO] - root - INFO - Loaded 216 rows of AVAX/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:54,868 - [BITVAVO] - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:04:54,868 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:54,869 - [BITVAVO] - root - INFO - Loaded 216 rows of AVAX/USDT data from cache (after filtering).
2025-07-16 06:04:54,929 - [BITVAVO] - root - INFO - Loaded 216 rows of ADA/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:54,930 - [BITVAVO] - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:04:54,931 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:54,931 - [BITVAVO] - root - INFO - Loaded 216 rows of ADA/USDT data from cache (after filtering).
2025-07-16 06:04:54,996 - [BITVAVO] - root - INFO - Loaded 216 rows of LINK/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:54,999 - [BITVAVO] - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:04:55,000 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:55,000 - [BITVAVO] - root - INFO - Loaded 216 rows of LINK/USDT data from cache (after filtering).
2025-07-16 06:04:55,043 - [BITVAVO] - root - INFO - Loaded 216 rows of TRX/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:55,046 - [BITVAVO] - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:04:55,047 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:55,047 - [BITVAVO] - root - INFO - Loaded 216 rows of TRX/USDT data from cache (after filtering).
2025-07-16 06:04:55,071 - [BITVAVO] - root - INFO - Loaded 216 rows of PEPE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:55,076 - [BITVAVO] - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:04:55,077 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:55,077 - [BITVAVO] - root - INFO - Loaded 216 rows of PEPE/USDT data from cache (after filtering).
2025-07-16 06:04:55,140 - [BITVAVO] - root - INFO - Loaded 216 rows of DOGE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:55,143 - [BITVAVO] - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:04:55,148 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:55,148 - [BITVAVO] - root - INFO - Loaded 216 rows of DOGE/USDT data from cache (after filtering).
2025-07-16 06:04:55,188 - [BITVAVO] - root - INFO - Loaded 216 rows of BNB/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:55,189 - [BITVAVO] - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:04:55,190 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:55,190 - [BITVAVO] - root - INFO - Loaded 216 rows of BNB/USDT data from cache (after filtering).
2025-07-16 06:04:55,226 - [BITVAVO] - root - INFO - Loaded 216 rows of DOT/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:55,227 - [BITVAVO] - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:04:55,229 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:55,229 - [BITVAVO] - root - INFO - Loaded 216 rows of DOT/USDT data from cache (after filtering).
2025-07-16 06:04:55,229 - [BITVAVO] - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-16 06:04:55,230 - [BITVAVO] - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:04:55,230 - [BITVAVO] - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:04:55,230 - [BITVAVO] - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:04:55,230 - [BITVAVO] - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:04:55,230 - [BITVAVO] - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:04:55,230 - [BITVAVO] - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:04:55,230 - [BITVAVO] - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:04:55,231 - [BITVAVO] - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:04:55,231 - [BITVAVO] - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:04:55,231 - [BITVAVO] - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:04:55,231 - [BITVAVO] - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:04:55,231 - [BITVAVO] - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:04:55,233 - [BITVAVO] - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:04:55,233 - [BITVAVO] - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:04:55,318 - [BITVAVO] - root - INFO - Using standard MTPI warmup period of 120 days
2025-07-16 06:04:55,320 - [BITVAVO] - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-07-16 06:04:55,320 - [BITVAVO] - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-07-16 06:04:55,320 - [BITVAVO] - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-07-16 06:04:55,320 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:04:55,346 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-16 06:04:55,346 - [BITVAVO] - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-07-16 06:04:55,346 - [BITVAVO] - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-07-16 06:04:55,346 - [BITVAVO] - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 06:04:55,346 - [BITVAVO] - root - INFO - Override: combination_method = consensus
2025-07-16 06:04:55,346 - [BITVAVO] - root - INFO - Override: long_threshold = 0.1
2025-07-16 06:04:55,346 - [BITVAVO] - root - INFO - Override: short_threshold = -0.1
2025-07-16 06:04:55,346 - [BITVAVO] - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-07-16 06:04:55,347 - [BITVAVO] - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-16 06:04:55,347 - [BITVAVO] - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-07-16 06:04:55,347 - [BITVAVO] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-16 06:04:55,390 - [BITVAVO] - root - INFO - Loaded 276 rows of BTC/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:04:55,393 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:04:55,393 - [BITVAVO] - root - INFO - Loaded 276 rows of BTC/USDT data from cache (after filtering).
2025-07-16 06:04:55,393 - [BITVAVO] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-16 06:04:55,393 - [BITVAVO] - root - INFO - Fetched BTC data: 276 candles from 2024-10-13 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:04:55,393 - [BITVAVO] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-16 06:04:55,589 - [BITVAVO] - root - INFO - Generated PGO Score signals: {-1: np.int64(111), 0: np.int64(34), 1: np.int64(131)}
2025-07-16 06:04:55,589 - [BITVAVO] - root - INFO - Generated pgo signals: 276 values
2025-07-16 06:04:55,589 - [BITVAVO] - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-07-16 06:04:55,589 - [BITVAVO] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-16 06:04:55,654 - [BITVAVO] - root - INFO - Generated BB Score signals: {-1: np.int64(109), 0: np.int64(32), 1: np.int64(135)}
2025-07-16 06:04:55,654 - [BITVAVO] - root - INFO - Generated Bollinger Band signals: 276 values
2025-07-16 06:04:55,654 - [BITVAVO] - root - INFO - Generated bollinger_bands signals: 276 values
2025-07-16 06:04:56,538 - [BITVAVO] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-16 06:04:56,539 - [BITVAVO] - root - INFO - Generated dwma_score signals: 276 values
2025-07-16 06:04:56,625 - [BITVAVO] - root - INFO - Generated DEMA Supertrend signals
2025-07-16 06:04:56,625 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(156), 0: np.int64(1), 1: np.int64(119)}
2025-07-16 06:04:56,625 - [BITVAVO] - root - INFO - Generated DEMA Super Score signals
2025-07-16 06:04:56,625 - [BITVAVO] - root - INFO - Generated dema_super_score signals: 276 values
2025-07-16 06:04:56,814 - [BITVAVO] - root - INFO - Generated DPSD signals
2025-07-16 06:04:56,814 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(105), 0: np.int64(87), 1: np.int64(84)}
2025-07-16 06:04:56,814 - [BITVAVO] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-16 06:04:56,814 - [BITVAVO] - root - INFO - Generated dpsd_score signals: 276 values
2025-07-16 06:04:56,829 - [BITVAVO] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-16 06:04:56,829 - [BITVAVO] - root - INFO - Generated AAD Score signals using SMA method
2025-07-16 06:04:56,829 - [BITVAVO] - root - INFO - Generated aad_score signals: 276 values
2025-07-16 06:04:56,930 - [BITVAVO] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-16 06:04:56,931 - [BITVAVO] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-16 06:04:56,931 - [BITVAVO] - root - INFO - Generated dynamic_ema_score signals: 276 values
2025-07-16 06:04:57,116 - [BITVAVO] - root - INFO - Generated quantile_dema_score signals: 276 values
2025-07-16 06:04:57,128 - [BITVAVO] - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-07-16 06:04:57,129 - [BITVAVO] - root - INFO - Signal distribution: {1: 151, -1: 124, 0: 1}
2025-07-16 06:04:57,129 - [BITVAVO] - root - INFO - Generated combined MTPI signals: 276 values using consensus method
2025-07-16 06:04:57,130 - [BITVAVO] - root - INFO - Signal distribution: {1: 151, -1: 124, 0: 1}
2025-07-16 06:04:57,130 - [BITVAVO] - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-07-16 06:04:57,134 - [BITVAVO] - root - INFO - Saving configuration to config/settings.yaml...
2025-07-16 06:04:57,141 - [BITVAVO] - root - INFO - Configuration saved successfully.
2025-07-16 06:04:57,141 - [BITVAVO] - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-07-16 06:04:57,141 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:04:57,153 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-16 06:04:57,153 - [BITVAVO] - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-07-16 06:04:57,153 - [BITVAVO] - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-07-16 06:04:57,153 - [BITVAVO] - root - INFO - Using ratio calculation method: independent
2025-07-16 06:04:57,193 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:57,242 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:04:57,275 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:04:57,275 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:57,310 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:04:57,323 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:04:57,361 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-16 06:04:57,361 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:57,390 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-16 06:04:57,399 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:04:57,432 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:04:57,433 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:57,462 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:04:57,471 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:04:57,503 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:04:57,503 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:57,532 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:04:57,541 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:04:57,578 - [BITVAVO] - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-07-16 06:04:57,578 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:57,610 - [BITVAVO] - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-07-16 06:04:57,620 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:04:57,658 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:04:57,658 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:57,688 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:04:57,698 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:04:57,730 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:04:57,730 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:57,771 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:04:57,782 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:04:57,817 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:04:57,817 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:57,869 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:04:57,884 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:04:57,921 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:04:57,921 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:57,957 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:04:57,966 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:04:58,000 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:04:58,000 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:58,032 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:04:58,042 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:04:58,077 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:58,117 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:04:58,150 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-16 06:04:58,151 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:58,182 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-16 06:04:58,192 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:04:58,230 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:04:58,230 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:58,271 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:04:58,280 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:04:58,311 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:58,347 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:04:58,382 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:04:58,382 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:58,414 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:04:58,423 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:04:58,458 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:04:58,458 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:58,487 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:04:58,497 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:04:58,531 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:04:58,531 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:58,562 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:04:58,572 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:04:58,603 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-16 06:04:58,603 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:58,634 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-16 06:04:58,644 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:04:58,679 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:04:58,679 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:58,708 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:04:58,719 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:04:58,753 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:04:58,754 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:58,781 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:04:58,791 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:04:58,824 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:04:58,824 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:58,851 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:04:58,860 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:04:58,893 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:04:58,893 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:58,924 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:04:58,935 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:04:58,977 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:59,061 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:04:59,105 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:59,151 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:04:59,219 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-16 06:04:59,219 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:59,272 - [BITVAVO] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-16 06:04:59,293 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:04:59,370 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-16 06:04:59,371 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:59,448 - [BITVAVO] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-16 06:04:59,466 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:04:59,552 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:59,633 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:04:59,692 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:04:59,692 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:59,728 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:04:59,738 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:04:59,785 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:04:59,785 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:04:59,866 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:04:59,898 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:04:59,968 - [BITVAVO] - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-16 06:04:59,970 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:00,031 - [BITVAVO] - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-16 06:05:00,048 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:00,106 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:05:00,106 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:00,158 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:05:00,175 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:00,230 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:00,231 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:00,279 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:00,295 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:00,350 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:00,350 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:00,402 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:00,418 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:00,473 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-16 06:05:00,473 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:00,522 - [BITVAVO] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-16 06:05:00,538 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:00,593 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:05:00,593 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:00,659 - [BITVAVO] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:05:00,674 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:00,729 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:00,730 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:00,780 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:00,804 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:00,840 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:00,841 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:00,871 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:00,886 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:00,919 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:00,920 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:00,948 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:00,957 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:00,992 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:00,992 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:01,030 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:01,043 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:01,097 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:01,138 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:01,185 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:01,228 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:01,272 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:01,323 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:01,362 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-16 06:05:01,362 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:01,394 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-16 06:05:01,405 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:01,444 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:01,487 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:01,535 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:01,582 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:01,620 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:01,620 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:01,665 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:01,683 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:01,722 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:01,771 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:01,810 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:01,857 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:01,910 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:01,910 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:01,948 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:01,958 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:01,998 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:02,082 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:02,166 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:02,239 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:02,301 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-16 06:05:02,301 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:02,350 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-16 06:05:02,366 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:02,421 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:02,473 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:02,509 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:02,550 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:02,591 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:02,592 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:02,638 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:02,650 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:02,690 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:02,690 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:02,723 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:02,734 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:02,767 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-16 06:05:02,768 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:02,803 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-16 06:05:02,813 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:02,851 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:02,852 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:02,888 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:02,899 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:02,937 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:02,937 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:02,971 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:02,981 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:03,021 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:03,021 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:03,055 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:03,066 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:03,100 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:03,100 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:03,133 - [BITVAVO] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:03,144 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:03,178 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:03,223 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:03,300 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:03,387 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:03,426 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:03,426 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:03,499 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:03,519 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:03,588 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:03,672 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:03,746 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:03,832 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:03,886 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:03,926 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:03,973 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:04,016 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:04,052 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:04,098 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:04,132 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:04,133 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:04,163 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:04,174 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:04,210 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:04,210 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:04,252 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:04,264 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:04,298 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:04,298 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:04,331 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:04,342 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:04,380 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:04,421 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:04,456 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:04,498 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:04,537 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:04,537 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:04,572 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:04,584 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:04,625 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:04,669 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:04,712 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:04,712 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:04,754 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:04,766 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:04,804 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:04,844 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:04,881 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:04,926 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:04,966 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:05,012 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:05,048 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:05,093 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:05,131 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:05,176 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:05,214 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-16 06:05:05,214 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:05,268 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-16 06:05:05,284 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:05,331 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:05,386 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:05,443 - [BITVAVO] - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-07-16 06:05:05,444 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:05,510 - [BITVAVO] - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-07-16 06:05:05,533 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:05,589 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:05,673 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:05,714 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:05,758 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:05,834 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:05,874 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:05,904 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:05,979 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:06,022 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:06,061 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:06,097 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:06,098 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:06,168 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:06,190 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:06,239 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:06,276 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:06,309 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:06,389 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:06,446 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:06,447 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:06,500 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:06,516 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:06,600 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:06,692 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:06,766 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:05:06,768 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:06,835 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:05:06,860 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:06,938 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:06,940 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:07,005 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:07,025 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:07,102 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-16 06:05:07,102 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:07,145 - [BITVAVO] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-16 06:05:07,155 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:07,189 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:07,229 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:07,319 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:07,319 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:07,382 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:07,403 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:07,488 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:07,490 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:07,534 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:07,544 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:07,578 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:07,640 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:07,677 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:07,677 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:07,710 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:07,721 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:07,771 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:07,874 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:07,944 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:08,001 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:08,101 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:08,156 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:08,244 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:08,289 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:08,322 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:08,377 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:08,459 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-16 06:05:08,459 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:08,497 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-16 06:05:08,507 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:08,545 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:08,631 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:08,671 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:08,738 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:08,835 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:08,930 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:09,006 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:09,008 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:09,041 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:09,051 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:09,105 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:09,188 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:09,256 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:09,300 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:09,337 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:09,409 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:09,491 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:09,579 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:09,627 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:09,667 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:09,718 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:09,721 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:09,772 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:09,781 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:09,813 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:09,850 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:09,884 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:09,885 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:09,947 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:09,964 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:10,036 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:10,036 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:10,098 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:10,107 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:10,153 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:10,154 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:10,194 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:10,215 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:10,290 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:10,344 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:10,401 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:10,403 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:10,471 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:10,492 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:10,569 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:10,570 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:10,638 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:10,658 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:10,749 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:10,877 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:10,996 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:11,125 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:11,190 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:11,287 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:11,395 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:11,493 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:11,572 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:11,656 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:11,734 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:11,814 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:11,887 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:11,991 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:12,072 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:12,168 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:12,287 - [BITVAVO] - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-07-16 06:05:12,289 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:12,357 - [BITVAVO] - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-07-16 06:05:12,377 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:12,454 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:12,455 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:12,518 - [BITVAVO] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:12,538 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:12,617 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:12,620 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:12,691 - [BITVAVO] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:12,714 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:12,803 - [BITVAVO] - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-07-16 06:05:12,805 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:12,879 - [BITVAVO] - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-07-16 06:05:12,903 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:12,988 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:13,066 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:13,160 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:13,261 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:13,339 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:13,412 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:13,480 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:13,556 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:13,631 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:13,711 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:13,776 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:13,853 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:13,919 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:14,003 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:14,068 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:14,069 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:14,125 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:14,144 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:14,217 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:14,217 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:14,284 - [BITVAVO] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:14,311 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:14,408 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:14,409 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:14,470 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:14,490 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:14,568 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-16 06:05:14,570 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:14,638 - [BITVAVO] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-16 06:05:14,661 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:14,735 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-16 06:05:14,737 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:14,806 - [BITVAVO] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-16 06:05:14,825 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:14,906 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:14,908 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:14,974 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:14,993 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:15,109 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:15,111 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:15,175 - [BITVAVO] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:15,194 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:15,262 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-16 06:05:15,264 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:15,324 - [BITVAVO] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-16 06:05:15,344 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:15,417 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:15,497 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:15,574 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:15,574 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:15,642 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:15,668 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:15,745 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:15,851 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:15,918 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:15,999 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:16,116 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:16,116 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:16,181 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:16,202 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:16,277 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:16,279 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:16,352 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:16,373 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:16,444 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:16,446 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:16,511 - [BITVAVO] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:16,533 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:16,610 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:05:16,610 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:16,677 - [BITVAVO] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:05:16,697 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:16,762 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-16 06:05:16,765 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:16,826 - [BITVAVO] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-16 06:05:16,846 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:16,918 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:16,920 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:16,983 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:17,003 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:17,078 - [BITVAVO] - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-07-16 06:05:17,080 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:17,138 - [BITVAVO] - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-07-16 06:05:17,158 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:17,232 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:17,233 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:17,299 - [BITVAVO] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:17,329 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:17,416 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:17,416 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:17,478 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:17,498 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:17,579 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:17,675 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:17,743 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:17,829 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:17,902 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:17,985 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:18,060 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,146 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:18,221 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,305 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:18,379 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:18,379 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,448 - [BITVAVO] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:18,468 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:18,534 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:18,535 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,594 - [BITVAVO] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:18,613 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:18,678 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:18,681 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,751 - [BITVAVO] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:18,782 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:18,851 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-16 06:05:18,853 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,909 - [BITVAVO] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-16 06:05:18,927 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:18,993 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-16 06:05:18,995 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:19,092 - [BITVAVO] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-16 06:05:19,122 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:19,197 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:19,199 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:19,261 - [BITVAVO] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:19,281 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:19,355 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:19,449 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:19,526 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:19,527 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:19,589 - [BITVAVO] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:19,609 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:19,677 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:19,765 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:19,839 - [BITVAVO] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:19,932 - [BITVAVO] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:28,581 - [BITVAVO] - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-07-16 06:05:28,583 - [BITVAVO] - root - INFO - Latest MTPI signal is 1
2025-07-16 06:05:28,583 - [BITVAVO] - root - INFO - Latest MTPI signal is bullish (1), will proceed with normal asset selection
2025-07-16 06:05:28,583 - [BITVAVO] - root - INFO - Finished calculating daily scores. DataFrame shape: (216, 14)
2025-07-16 06:05:28,584 - [BITVAVO] - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering ENABLED
2025-07-16 06:05:28,596 - [BITVAVO] - root - INFO - Date ranges for each asset:
2025-07-16 06:05:28,597 - [BITVAVO] - root - INFO -   ETH/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:28,597 - [BITVAVO] - root - INFO -   BTC/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:28,597 - [BITVAVO] - root - INFO -   SOL/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:28,597 - [BITVAVO] - root - INFO -   SUI/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:28,597 - [BITVAVO] - root - INFO -   XRP/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:28,599 - [BITVAVO] - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:28,599 - [BITVAVO] - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:28,600 - [BITVAVO] - root - INFO -   ADA/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:28,600 - [BITVAVO] - root - INFO -   LINK/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:28,600 - [BITVAVO] - root - INFO -   TRX/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:28,600 - [BITVAVO] - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:28,600 - [BITVAVO] - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:28,600 - [BITVAVO] - root - INFO -   BNB/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:28,600 - [BITVAVO] - root - INFO -   DOT/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:28,600 - [BITVAVO] - root - INFO - Common dates range: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:28,600 - [BITVAVO] - root - INFO - Analysis will run from: 2025-02-10 to 2025-07-15 (156 candles)
2025-07-16 06:05:28,612 - [BITVAVO] - root - INFO - EXECUTION TIMING VERIFICATION:
2025-07-16 06:05:28,612 - [BITVAVO] - root - INFO -    Execution Method: candle_close
2025-07-16 06:05:28,612 - [BITVAVO] - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-07-16 06:05:28,612 - [BITVAVO] - root - INFO -    Signal generated and executed immediately
2025-07-16 06:05:28,638 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-10 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 10.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 8.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:28,638 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,638 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,638 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,639 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,639 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,639 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,639 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,639 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,639 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,641 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,641 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,641 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,642 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,642 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,642 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,642 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,642 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,642 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,646 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-11 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 9.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:28,646 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,646 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,646 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,646 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,647 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,647 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,647 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,647 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,647 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,647 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,647 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,649 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,649 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,649 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,650 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,650 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,650 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,650 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,654 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-12 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 9.0, 'SUI/USDT': 4.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:28,654 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,654 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,654 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,655 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,655 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,655 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,655 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,655 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,655 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,657 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,657 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,657 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,658 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,658 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,658 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,658 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,658 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,658 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,659 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-13 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:28,661 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,662 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,662 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,662 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,662 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,662 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,662 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,662 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,662 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,662 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,663 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,665 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,665 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,666 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,666 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,666 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,666 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,666 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,668 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-14 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:28,670 - [BITVAVO] - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,670 - [BITVAVO] - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,671 - [BITVAVO] - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,671 - [BITVAVO] - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,671 - [BITVAVO] - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,671 - [BITVAVO] - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,671 - [BITVAVO] - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,671 - [BITVAVO] - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,671 - [BITVAVO] - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,671 - [BITVAVO] - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,671 - [BITVAVO] - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,671 - [BITVAVO] - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,671 - [BITVAVO] - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,671 - [BITVAVO] - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:28,671 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,671 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,672 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,672 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,675 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-15 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:28,675 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,675 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,676 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,676 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,679 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-16 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 5.0}
2025-07-16 06:05:28,679 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,679 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,679 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,680 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,683 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-17 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:28,683 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,683 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,683 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,683 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,687 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-18 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:28,687 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,687 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,687 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,687 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,690 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-19 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 5.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 8.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:28,691 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,691 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,691 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,691 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,695 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-20 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 8.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 5.0}
2025-07-16 06:05:28,695 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,695 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,695 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,695 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,698 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-21 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:28,700 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,700 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,701 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,701 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,702 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-22 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 6.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 13.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:28,704 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,704 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,704 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,704 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,706 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-23 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 5.0}
2025-07-16 06:05:28,706 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,706 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,706 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,708 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,709 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-24 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:28,709 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,710 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,710 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,710 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,713 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-25 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 2.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:28,713 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,713 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,713 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,713 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,716 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-26 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:28,716 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,717 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,717 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,717 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,718 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-27 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 5.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 5.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 10.0}
2025-07-16 06:05:28,720 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,720 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,721 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,721 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,722 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-28 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 5.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 12.0, 'DOT/USDT': 11.0}
2025-07-16 06:05:28,722 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,724 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,724 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,724 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,726 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-01 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 2.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 11.0}
2025-07-16 06:05:28,726 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,726 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,726 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,728 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,729 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-02 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 1.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 9.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:28,730 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,730 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,730 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,730 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,733 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-03 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 2.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 9.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:28,733 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,734 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,734 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,734 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,737 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-04 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 1.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 13.0, 'LINK/USDT': 2.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:28,737 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,737 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,737 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,737 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,741 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-05 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 1.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 7.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:28,741 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,741 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,741 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,741 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,744 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-06 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:28,744 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,745 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,745 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,745 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,746 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-07 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:28,747 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,748 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,748 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,749 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,750 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-08 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:28,750 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,753 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,754 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,754 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,757 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-09 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:28,757 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,757 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,758 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,758 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,762 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-10 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:28,762 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,762 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,762 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,762 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,765 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-11 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:28,766 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,766 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,766 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,766 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,768 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-12 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:28,768 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,768 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,768 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,768 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,778 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-13 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 9.0, 'SOL/USDT': 3.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:28,778 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,778 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,778 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,778 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,779 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-14 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 6.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:28,780 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,780 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,780 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,780 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,783 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-15 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 6.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 11.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:28,784 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,784 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,784 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,784 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,787 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-16 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 10.0, 'LINK/USDT': 5.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 12.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:28,787 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,787 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,787 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,788 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,791 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-17 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 10.0, 'LINK/USDT': 5.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:28,791 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,791 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,791 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,791 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,795 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-18 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 9.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:28,795 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,795 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,795 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,795 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,798 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-19 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 9.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:28,798 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,799 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,799 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,799 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,800 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-20 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:28,800 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,800 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,802 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,802 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,803 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-21 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:28,804 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,804 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,804 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,804 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,807 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-22 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:28,807 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,807 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,807 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,808 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,811 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-23 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:28,811 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,811 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,811 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,811 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,814 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-24 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 0.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 13.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:28,814 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,815 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,815 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,815 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,816 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-25 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 5.0, 'SOL/USDT': 4.0, 'SUI/USDT': 0.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:28,818 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,818 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,818 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,819 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,820 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-26 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 10.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:28,820 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,820 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,820 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,820 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,823 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-27 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 9.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:28,823 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,823 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,823 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,823 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,827 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-28 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:28,827 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,827 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,827 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,827 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,828 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-29 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:28,830 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,830 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,830 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,830 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,832 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-30 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:28,832 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,832 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,832 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,832 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,835 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-31 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 7.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:28,835 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,836 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,836 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,836 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,839 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-01 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 7.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:28,839 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,839 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,839 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,839 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,842 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-02 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:28,843 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,843 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,843 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,843 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,844 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-03 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:28,844 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,846 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,846 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,846 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,848 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-04 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 8.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:28,848 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,848 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,848 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,850 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,851 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-05 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 8.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:28,852 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,852 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,852 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,852 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,855 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-06 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 5.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 12.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:28,855 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,855 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,855 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,855 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,859 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-07 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 11.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:28,859 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,859 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,859 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,859 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,862 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-08 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 5.0, 'SUI/USDT': 5.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 5.0}
2025-07-16 06:05:28,862 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,863 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,863 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,863 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,864 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-09 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 1.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:28,866 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,866 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,867 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,867 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,868 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-10 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:28,870 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,870 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,870 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,870 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,872 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-11 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:28,872 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,872 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,872 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,874 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,875 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-12 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:28,878 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,878 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,878 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,878 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,880 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-13 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:28,880 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,880 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,880 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,880 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,883 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-14 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:28,884 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,884 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,884 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,884 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,887 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-15 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:28,887 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,888 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,888 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,888 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,889 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-16 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:28,889 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,891 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,891 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,891 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,893 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-17 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 12.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:28,893 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,893 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,893 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,893 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,896 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-18 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 12.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:28,897 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,897 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,897 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,897 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,900 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-19 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 4.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:28,900 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,900 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,900 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,900 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,904 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-20 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 3.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 5.0}
2025-07-16 06:05:28,904 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,904 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,904 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,904 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,907 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-21 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 3.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:28,908 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,908 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,908 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,908 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,909 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-22 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 12.0, 'SUI/USDT': 9.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 5.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 5.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:28,911 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,911 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,911 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,911 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:28,912 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-04-23:
2025-07-16 06:05:28,912 - [BITVAVO] - root - INFO -    Signal Date: 2025-04-22 (generated at 00:00 UTC)
2025-07-16 06:05:28,912 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-23 00:00 UTC (immediate)
2025-07-16 06:05:28,912 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:28,912 - [BITVAVO] - root - INFO -    Buying: ['SOL/USDT']
2025-07-16 06:05:28,912 - [BITVAVO] - root - INFO -    SOL/USDT buy price: $151.1000 (close price)
2025-07-16 06:05:28,916 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-23 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:28,916 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,916 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,916 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,916 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,916 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-07-16 06:05:28,916 - [BITVAVO] - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-07-16 06:05:28,916 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-07-16 06:05:28,917 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:28,917 - [BITVAVO] - root - INFO -    Selling: ['SOL/USDT']
2025-07-16 06:05:28,917 - [BITVAVO] - root - INFO -    Buying: ['SUI/USDT']
2025-07-16 06:05:28,917 - [BITVAVO] - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-07-16 06:05:28,920 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-24 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:28,921 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,921 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,921 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,921 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,925 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-25 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:28,925 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,925 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,925 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,927 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,929 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-26 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:28,929 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,929 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,929 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,929 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,932 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-27 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:28,933 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,933 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,933 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,933 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,936 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-28 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:28,937 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,937 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,937 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,937 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,940 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-29 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:28,940 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,941 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,941 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,941 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,944 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-30 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:28,944 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,944 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,944 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,944 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,948 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-01 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:28,948 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,948 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,948 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,948 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,952 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-02 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 8.0, 'SOL/USDT': 10.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:28,952 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,952 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,952 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,952 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,956 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-03 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:28,956 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,956 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,956 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,956 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,960 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-04 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:28,960 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,960 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,960 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,960 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,963 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-05 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:28,964 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,964 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,964 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,964 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,968 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-06 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:28,968 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,969 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,969 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,969 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,973 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-07 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 0.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:28,974 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,974 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,974 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,975 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,978 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-08 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 6.0, 'SOL/USDT': 9.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:28,978 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,978 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,978 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,978 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,982 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-09 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 4.0, 'SOL/USDT': 9.0, 'SUI/USDT': 12.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 1.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:28,984 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,985 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,985 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,985 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,985 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-07-16 06:05:28,986 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-07-16 06:05:28,987 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-07-16 06:05:28,987 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:28,987 - [BITVAVO] - root - INFO -    Selling: ['SUI/USDT']
2025-07-16 06:05:28,987 - [BITVAVO] - root - INFO -    Buying: ['PEPE/USDT']
2025-07-16 06:05:28,987 - [BITVAVO] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-16 06:05:28,990 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-10 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 12.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:28,991 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,991 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,991 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,991 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:28,992 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-11 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 12.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:28,996 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:28,996 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:28,997 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:28,997 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,000 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-12 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 11.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:29,001 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,001 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,001 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,001 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,005 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-13 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:29,005 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,005 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,005 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,005 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,009 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-14 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:29,009 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,009 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,009 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,009 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,013 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-15 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:29,013 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,013 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,013 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,013 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,017 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-16 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:29,017 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,017 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,017 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,017 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,020 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-17 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 4.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:29,021 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,021 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,021 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,021 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,024 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-18 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 4.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:29,025 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,025 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,025 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,025 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,028 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-19 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 5.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:29,029 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,029 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,029 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,029 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,032 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-20 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:29,033 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,033 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,033 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,033 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,033 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-07-16 06:05:29,033 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-07-16 06:05:29,033 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-07-16 06:05:29,033 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:29,034 - [BITVAVO] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-16 06:05:29,034 - [BITVAVO] - root - INFO -    Buying: ['AAVE/USDT']
2025-07-16 06:05:29,034 - [BITVAVO] - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-07-16 06:05:29,037 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-21 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:29,038 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,038 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,038 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,038 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,042 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-22 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 1.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:29,042 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,044 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,044 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,044 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,044 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-07-16 06:05:29,045 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-07-16 06:05:29,045 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-07-16 06:05:29,045 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:29,045 - [BITVAVO] - root - INFO -    Selling: ['AAVE/USDT']
2025-07-16 06:05:29,045 - [BITVAVO] - root - INFO -    Buying: ['PEPE/USDT']
2025-07-16 06:05:29,045 - [BITVAVO] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-16 06:05:29,048 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-23 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 9.0, 'SUI/USDT': 7.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 2.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:29,049 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,049 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,049 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,049 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,052 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-24 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 9.0, 'SUI/USDT': 6.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 2.0, 'LINK/USDT': 1.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 5.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:29,052 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,053 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,053 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,053 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,053 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-05-25:
2025-07-16 06:05:29,053 - [BITVAVO] - root - INFO -    Signal Date: 2025-05-24 (generated at 00:00 UTC)
2025-07-16 06:05:29,053 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-25 00:00 UTC (immediate)
2025-07-16 06:05:29,053 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:29,053 - [BITVAVO] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-16 06:05:29,053 - [BITVAVO] - root - INFO -    Buying: ['AAVE/USDT']
2025-07-16 06:05:29,053 - [BITVAVO] - root - INFO -    AAVE/USDT buy price: $269.2800 (close price)
2025-07-16 06:05:29,057 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-25 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 3.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 5.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:29,057 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,057 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,057 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,057 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,061 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-26 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 6.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,061 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,061 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,061 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,061 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,065 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-27 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 7.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,065 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,065 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,065 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,065 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,068 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-28 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 8.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 2.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:29,068 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,068 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,068 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,068 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,073 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-29 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 2.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:29,073 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,073 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,073 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,073 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,077 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-30 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,077 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,077 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,077 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,077 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,081 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-31 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,081 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,081 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,081 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,081 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,085 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-01 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,085 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,085 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,085 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,085 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,089 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-02 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,089 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,089 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,089 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,089 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,093 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-03 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,093 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,093 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,093 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,093 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,097 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-04 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,097 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,097 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,097 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,097 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,101 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-05 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:29,101 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,101 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,102 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,102 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,105 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-06 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:29,106 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,106 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,106 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,106 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,110 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-07 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:29,110 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,112 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,112 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,113 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,116 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-08 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:29,117 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,117 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,117 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,117 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,121 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-09 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 1.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,121 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,121 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,121 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,122 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,125 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-10 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,126 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,126 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,126 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,128 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,130 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-11 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,130 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,130 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,130 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,130 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,134 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-12 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,134 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,134 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,134 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,134 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,137 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-13 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 1.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,138 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,138 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,138 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,138 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,141 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-14 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,141 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,141 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,141 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,142 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,145 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-15 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,145 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,145 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,145 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,145 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,149 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-16 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:29,149 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,149 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,149 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,149 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,152 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-17 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:29,153 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,153 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,153 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,153 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,156 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-18 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:29,156 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,156 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,157 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,157 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,160 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-19 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:29,160 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,161 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,161 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,161 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,162 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-20 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:29,164 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,164 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,164 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,165 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,165 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-07-16 06:05:29,165 - [BITVAVO] - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-07-16 06:05:29,165 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-07-16 06:05:29,165 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:29,165 - [BITVAVO] - root - INFO -    Selling: ['AAVE/USDT']
2025-07-16 06:05:29,166 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-21 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 11.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:29,168 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,168 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,168 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,168 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,170 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-22 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:29,170 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,170 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,170 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,170 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,173 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-23 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,173 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,173 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,174 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,174 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,177 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-24 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,177 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,177 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,177 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,177 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,180 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-25 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,181 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,181 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,181 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,181 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,182 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-26 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,182 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,184 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,184 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,184 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,186 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-27 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,188 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,188 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,189 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,189 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,190 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-28 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 13.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,192 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,193 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,193 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,193 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,194 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-29 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:29,196 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,196 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,196 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,197 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,198 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-30 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:29,198 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,198 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,198 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,198 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,199 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-01 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:29,200 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,200 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,200 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,200 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,205 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-02 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:29,206 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,206 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,206 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,206 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,209 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-03 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,209 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,210 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,210 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,210 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,213 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-04 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,213 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,214 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,214 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,214 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,217 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-05 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:29,217 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,218 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,218 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,218 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,221 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-06 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:29,222 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,222 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,222 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,222 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,225 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-07 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:29,226 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,226 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,226 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,226 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,229 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-08 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 6.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:29,230 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,230 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,230 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,230 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,233 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-09 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 4.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:29,234 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,234 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,234 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,234 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,237 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-10 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 4.0, 'SUI/USDT': 11.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:29,238 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,238 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,238 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,238 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:29,240 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-07-11:
2025-07-16 06:05:29,240 - [BITVAVO] - root - INFO -    Signal Date: 2025-07-10 (generated at 00:00 UTC)
2025-07-16 06:05:29,241 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-11 00:00 UTC (immediate)
2025-07-16 06:05:29,241 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:29,241 - [BITVAVO] - root - INFO -    Buying: ['SUI/USDT']
2025-07-16 06:05:29,241 - [BITVAVO] - root - INFO -    SUI/USDT buy price: $3.3868 (close price)
2025-07-16 06:05:29,245 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-11 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 3.0, 'SOL/USDT': 3.0, 'SUI/USDT': 10.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 8.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:29,245 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,245 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,245 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,246 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,246 - [BITVAVO] - root - INFO - ASSET CHANGE DETECTED on 2025-07-12:
2025-07-16 06:05:29,246 - [BITVAVO] - root - INFO -    Signal Date: 2025-07-11 (generated at 00:00 UTC)
2025-07-16 06:05:29,246 - [BITVAVO] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-12 00:00 UTC (immediate)
2025-07-16 06:05:29,246 - [BITVAVO] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:29,246 - [BITVAVO] - root - INFO -    Selling: ['SUI/USDT']
2025-07-16 06:05:29,246 - [BITVAVO] - root - INFO -    Buying: ['XRP/USDT']
2025-07-16 06:05:29,246 - [BITVAVO] - root - INFO -    XRP/USDT buy price: $2.7395 (close price)
2025-07-16 06:05:29,252 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-12 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 3.0, 'SUI/USDT': 10.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 9.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:29,252 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,253 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,253 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,253 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,256 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-13 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:29,256 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,256 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,257 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,257 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,258 - [BITVAVO] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-14 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 12.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:29,260 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:29,260 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:29,260 - [BITVAVO] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=imcumbent, previous_scores=False
2025-07-16 06:05:29,261 - [BITVAVO] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=imcumbent, current_holdings=True, filtered_scores=True
2025-07-16 06:05:29,412 - [BITVAVO] - root - INFO - Entry trade at 2025-04-23 00:00:00+00:00: SOL/USDT
2025-07-16 06:05:29,413 - [BITVAVO] - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-07-16 06:05:29,413 - [BITVAVO] - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-07-16 06:05:29,413 - [BITVAVO] - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-16 06:05:29,415 - [BITVAVO] - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-07-16 06:05:29,415 - [BITVAVO] - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-16 06:05:29,416 - [BITVAVO] - root - INFO - Exit trade at 2025-06-21 00:00:00+00:00 from AAVE/USDT
2025-07-16 06:05:29,416 - [BITVAVO] - root - INFO - Entry trade at 2025-07-11 00:00:00+00:00: SUI/USDT
2025-07-16 06:05:29,416 - [BITVAVO] - root - INFO - Swap trade at 2025-07-12 00:00:00+00:00: SUI/USDT -> XRP/USDT
2025-07-16 06:05:29,416 - [BITVAVO] - root - INFO - Total trades: 9 (Entries: 2, Exits: 1, Swaps: 6)
2025-07-16 06:05:29,421 - [BITVAVO] - root - INFO - Strategy execution completed in 0s
2025-07-16 06:05:29,421 - [BITVAVO] - root - INFO - DEBUG: self.elapsed_time = 0.8369529247283936 seconds
2025-07-16 06:05:29,453 - [BITVAVO] - root - INFO - Saved allocation history to allocation_history_1d_1d_with_mtpi_no_rebal_independent_imcumbent_2025-02-10.csv
2025-07-16 06:05:29,456 - [BITVAVO] - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-07-16 06:05:29,457 - [BITVAVO] - root - INFO - Assets included in buy-and-hold comparison:
2025-07-16 06:05:29,457 - [BITVAVO] - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-07-16 06:05:29,457 - [BITVAVO] - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-07-16 06:05:29,457 - [BITVAVO] - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-07-16 06:05:29,457 - [BITVAVO] - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-07-16 06:05:29,457 - [BITVAVO] - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-07-16 06:05:29,457 - [BITVAVO] - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-07-16 06:05:29,457 - [BITVAVO] - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-07-16 06:05:29,457 - [BITVAVO] - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-07-16 06:05:29,457 - [BITVAVO] - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-07-16 06:05:29,457 - [BITVAVO] - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-07-16 06:05:29,457 - [BITVAVO] - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-07-16 06:05:29,457 - [BITVAVO] - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-07-16 06:05:29,457 - [BITVAVO] - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-07-16 06:05:29,457 - [BITVAVO] - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-07-16 06:05:29,458 - [BITVAVO] - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-07-16 06:05:29,465 - [BITVAVO] - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:29,469 - [BITVAVO] - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:29,473 - [BITVAVO] - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:29,478 - [BITVAVO] - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:29,484 - [BITVAVO] - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:29,489 - [BITVAVO] - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:29,493 - [BITVAVO] - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:29,501 - [BITVAVO] - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:29,509 - [BITVAVO] - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:29,516 - [BITVAVO] - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:29,524 - [BITVAVO] - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:29,530 - [BITVAVO] - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:29,538 - [BITVAVO] - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:29,545 - [BITVAVO] - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:29,553 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 216 points
2025-07-16 06:05:29,554 - [BITVAVO] - root - INFO - ETH/USDT B&H total return: 17.91%
2025-07-16 06:05:29,560 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 216 points
2025-07-16 06:05:29,561 - [BITVAVO] - root - INFO - BTC/USDT B&H total return: 20.86%
2025-07-16 06:05:29,568 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 216 points
2025-07-16 06:05:29,569 - [BITVAVO] - root - INFO - SOL/USDT B&H total return: -18.20%
2025-07-16 06:05:29,574 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 216 points
2025-07-16 06:05:29,577 - [BITVAVO] - root - INFO - SUI/USDT B&H total return: 27.54%
2025-07-16 06:05:29,582 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 216 points
2025-07-16 06:05:29,582 - [BITVAVO] - root - INFO - XRP/USDT B&H total return: 20.44%
2025-07-16 06:05:29,589 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 216 points
2025-07-16 06:05:29,590 - [BITVAVO] - root - INFO - AAVE/USDT B&H total return: 29.65%
2025-07-16 06:05:29,597 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 216 points
2025-07-16 06:05:29,597 - [BITVAVO] - root - INFO - AVAX/USDT B&H total return: -12.31%
2025-07-16 06:05:29,604 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 216 points
2025-07-16 06:05:29,605 - [BITVAVO] - root - INFO - ADA/USDT B&H total return: 4.84%
2025-07-16 06:05:29,610 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 216 points
2025-07-16 06:05:29,612 - [BITVAVO] - root - INFO - LINK/USDT B&H total return: -14.35%
2025-07-16 06:05:29,618 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 216 points
2025-07-16 06:05:29,618 - [BITVAVO] - root - INFO - TRX/USDT B&H total return: 22.06%
2025-07-16 06:05:29,624 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 216 points
2025-07-16 06:05:29,626 - [BITVAVO] - root - INFO - PEPE/USDT B&H total return: 34.10%
2025-07-16 06:05:29,636 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 216 points
2025-07-16 06:05:29,637 - [BITVAVO] - root - INFO - DOGE/USDT B&H total return: -22.01%
2025-07-16 06:05:29,642 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 216 points
2025-07-16 06:05:29,643 - [BITVAVO] - root - INFO - BNB/USDT B&H total return: 11.52%
2025-07-16 06:05:29,648 - [BITVAVO] - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 216 points
2025-07-16 06:05:29,651 - [BITVAVO] - root - INFO - DOT/USDT B&H total return: -15.73%
2025-07-16 06:05:29,659 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:05:29,686 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:29,720 - [BITVAVO] - root - INFO - Using colored segments for single-asset strategy visualization
2025-07-16 06:05:30,098 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:05:30,123 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:36,443 - [BITVAVO] - root - INFO - Added ETH/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:36,445 - [BITVAVO] - root - INFO - Added BTC/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:36,446 - [BITVAVO] - root - INFO - Added SOL/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:36,446 - [BITVAVO] - root - INFO - Added SUI/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:36,446 - [BITVAVO] - root - INFO - Added XRP/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:36,446 - [BITVAVO] - root - INFO - Added AAVE/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:36,446 - [BITVAVO] - root - INFO - Added AVAX/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:36,446 - [BITVAVO] - root - INFO - Added ADA/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:36,446 - [BITVAVO] - root - INFO - Added LINK/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:36,446 - [BITVAVO] - root - INFO - Added TRX/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:36,446 - [BITVAVO] - root - INFO - Added PEPE/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:36,446 - [BITVAVO] - root - INFO - Added DOGE/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:36,446 - [BITVAVO] - root - INFO - Added BNB/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:36,446 - [BITVAVO] - root - INFO - Added DOT/USDT buy-and-hold curve with 216 points
2025-07-16 06:05:36,446 - [BITVAVO] - root - INFO - Added 14 buy-and-hold curves to results
2025-07-16 06:05:36,447 - [BITVAVO] - root - INFO -   - ETH/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:36,449 - [BITVAVO] - root - INFO -   - BTC/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:36,449 - [BITVAVO] - root - INFO -   - SOL/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:36,450 - [BITVAVO] - root - INFO -   - SUI/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:36,450 - [BITVAVO] - root - INFO -   - XRP/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:36,450 - [BITVAVO] - root - INFO -   - AAVE/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:36,450 - [BITVAVO] - root - INFO -   - AVAX/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:36,450 - [BITVAVO] - root - INFO -   - ADA/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:36,450 - [BITVAVO] - root - INFO -   - LINK/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:36,450 - [BITVAVO] - root - INFO -   - TRX/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:36,450 - [BITVAVO] - root - INFO -   - PEPE/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:36,451 - [BITVAVO] - root - INFO -   - DOGE/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:36,451 - [BITVAVO] - root - INFO -   - BNB/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:36,451 - [BITVAVO] - root - INFO -   - DOT/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:36,511 - [BITVAVO] - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-07-16 06:05:36,511 - [BITVAVO] - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-16 06:05:36,514 - [BITVAVO] - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-07-16 06:05:36,516 - [BITVAVO] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:05:36,545 - [BITVAVO] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:36,547 - [BITVAVO] - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 06:05:36,547 - [BITVAVO] - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 06:05:36,547 - [BITVAVO] - root - INFO - Combination method: consensus
2025-07-16 06:05:36,548 - [BITVAVO] - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-16 06:05:36,548 - [BITVAVO] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-16 06:05:36,592 - [BITVAVO] - root - INFO - Loaded 2157 rows of BTC/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:36,595 - [BITVAVO] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:36,596 - [BITVAVO] - root - INFO - Loaded 2157 rows of BTC/USDT data from cache (after filtering).
2025-07-16 06:05:36,596 - [BITVAVO] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-16 06:05:36,596 - [BITVAVO] - root - INFO - Fetched BTC data: 2157 candles from 2019-08-20 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:36,596 - [BITVAVO] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-16 06:05:37,386 - [BITVAVO] - root - INFO - Generated PGO Score signals: {-1: np.int64(996), 0: np.int64(34), 1: np.int64(1127)}
2025-07-16 06:05:37,388 - [BITVAVO] - root - INFO - PGO signal: 1
2025-07-16 06:05:37,388 - [BITVAVO] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-16 06:05:37,601 - [BITVAVO] - root - INFO - Generated BB Score signals: {-1: np.int64(993), 0: np.int64(33), 1: np.int64(1131)}
2025-07-16 06:05:37,603 - [BITVAVO] - root - INFO - Bollinger Bands signal: 1
2025-07-16 06:05:49,076 - [BITVAVO] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-16 06:05:49,077 - [BITVAVO] - root - INFO - DWMA Score signal: 1
2025-07-16 06:05:50,515 - [BITVAVO] - root - INFO - Generated DEMA Supertrend signals
2025-07-16 06:05:50,517 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1268), 0: np.int64(1), 1: np.int64(888)}
2025-07-16 06:05:50,518 - [BITVAVO] - root - INFO - Generated DEMA Super Score signals
2025-07-16 06:05:50,518 - [BITVAVO] - root - INFO - DEMA Super Score signal: 1
2025-07-16 06:05:54,089 - [BITVAVO] - root - INFO - Generated DPSD signals
2025-07-16 06:05:54,089 - [BITVAVO] - root - INFO - Signal distribution: {-1: np.int64(1087), 0: np.int64(87), 1: np.int64(983)}
2025-07-16 06:05:54,089 - [BITVAVO] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-16 06:05:54,089 - [BITVAVO] - root - INFO - DPSD Score signal: 1
2025-07-16 06:05:54,329 - [BITVAVO] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-16 06:05:54,329 - [BITVAVO] - root - INFO - Generated AAD Score signals using SMA method
2025-07-16 06:05:54,329 - [BITVAVO] - root - INFO - AAD Score signal: 1
2025-07-16 06:05:56,055 - [BITVAVO] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-16 06:05:56,055 - [BITVAVO] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-16 06:05:56,057 - [BITVAVO] - root - INFO - Dynamic EMA Score signal: 1
2025-07-16 06:05:59,275 - [BITVAVO] - root - INFO - Quantile DEMA Score signal: 1
2025-07-16 06:05:59,277 - [BITVAVO] - root - INFO - Individual signals: {'pgo': 1, 'bollinger_bands': 1, 'dwma_score': 1, 'dema_super_score': 1, 'dpsd_score': 1, 'aad_score': 1, 'dynamic_ema_score': 1, 'quantile_dema_score': 1}
2025-07-16 06:05:59,277 - [BITVAVO] - root - INFO - Combined MTPI signal (consensus): 1
2025-07-16 06:05:59,278 - [BITVAVO] - root - INFO - MTPI Score: 1.000000
2025-07-16 06:05:59,278 - [BITVAVO] - root - INFO - Added current MTPI score to results: 1.000000 (using 1d timeframe)
2025-07-16 06:05:59,286 - [BITVAVO] - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-07-16 06:05:59,286 - [BITVAVO] - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 13.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:59,286 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 13.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:59,286 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-16 06:05:59,286 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-16 06:05:59,287 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 9.0)
2025-07-16 06:05:59,287 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 3.0)
2025-07-16 06:05:59,287 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 2.0)
2025-07-16 06:05:59,287 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 13.0)
2025-07-16 06:05:59,287 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 12.0)
2025-07-16 06:05:59,287 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 6.0)
2025-07-16 06:05:59,287 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 6.0)
2025-07-16 06:05:59,287 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 10.0)
2025-07-16 06:05:59,287 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 7.0)
2025-07-16 06:05:59,289 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 1.0)
2025-07-16 06:05:59,289 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 9.0)
2025-07-16 06:05:59,289 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 5.0)
2025-07-16 06:05:59,289 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 0.0)
2025-07-16 06:05:59,290 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 4.0)
2025-07-16 06:05:59,290 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 06:05:59,290 - [BITVAVO] - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:05:59,290 - [BITVAVO] - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 06:05:59,303 - [BITVAVO] - root - INFO - Saved metrics to new file: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250709_run_20250716_060452.csv
2025-07-16 06:05:59,305 - [BITVAVO] - root - INFO - Saved performance metrics to CSV: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250709_run_20250716_060452.csv
2025-07-16 06:05:59,305 - [BITVAVO] - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-07-16 06:05:59,306 - [BITVAVO] - root - INFO - Results type: <class 'dict'>
2025-07-16 06:05:59,306 - [BITVAVO] - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-07-16 06:05:59,306 - [BITVAVO] - root - INFO - Success flag set to: True
2025-07-16 06:05:59,306 - [BITVAVO] - root - INFO - Message set to: Strategy calculation completed successfully
2025-07-16 06:05:59,306 - [BITVAVO] - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 216 entries
2025-07-16 06:05:59,306 - [BITVAVO] - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-07-16 06:05:59,306 - [BITVAVO] - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 216 entries
2025-07-16 06:05:59,306 - [BITVAVO] - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 276 entries
2025-07-16 06:05:59,306 - [BITVAVO] - root - INFO -   - mtpi_score: <class 'float'>
2025-07-16 06:05:59,306 - [BITVAVO] - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 216 entries
2025-07-16 06:05:59,306 - [BITVAVO] - root - INFO -   - performance_metrics: dict with 3 entries
2025-07-16 06:05:59,307 - [BITVAVO] - root - INFO -   - metrics_file: <class 'str'>
2025-07-16 06:05:59,307 - [BITVAVO] - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-07-16 06:05:59,307 - [BITVAVO] - root - INFO -   - success: <class 'bool'>
2025-07-16 06:05:59,307 - [BITVAVO] - root - INFO -   - message: <class 'str'>
2025-07-16 06:05:59,310 - [BITVAVO] - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-07-16 06:05:59,311 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: XRP/EUR
2025-07-16 06:05:59,314 - [BITVAVO] - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-07-11 00:00:00+00:00    SUI/EUR
2025-07-12 00:00:00+00:00    XRP/EUR
2025-07-13 00:00:00+00:00    XRP/EUR
2025-07-14 00:00:00+00:00    XRP/EUR
2025-07-15 00:00:00+00:00    XRP/EUR
dtype: object
2025-07-16 06:05:59,314 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 06:05:59,314 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:05:59,314 - [BITVAVO] - root - INFO - [DEBUG] TIE-BREAKING - Strategy: imcumbent
2025-07-16 06:05:59,314 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-07-16 06:05:59,314 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 06:05:59,314 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:05:59,315 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 13.0
2025-07-16 06:05:59,315 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 13.0: ['SUI/EUR']
2025-07-16 06:05:59,315 - [BITVAVO] - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: SUI/EUR
2025-07-16 06:05:59,315 - [BITVAVO] - root - ERROR - [DEBUG] SELECTED BEST ASSET: SUI/EUR (score: 13.0)
2025-07-16 06:05:59,315 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: SUI/EUR (MTPI signal: 1)
2025-07-16 06:05:59,315 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-07-16 06:05:59,315 - [BITVAVO] - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['SUI/EUR']
2025-07-16 06:05:59,315 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - UPDATING SELECTION: XRP/EUR -> SUI/EUR
2025-07-16 06:05:59,315 - [BITVAVO] - root - ERROR - [DEBUG] NO TIE - Single winner: SUI/EUR
2025-07-16 06:05:59,381 - [BITVAVO] - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-07-16 06:05:59,381 - [BITVAVO] - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:05:59,381 - [BITVAVO] - root - INFO - Single asset strategy with best asset: SUI/EUR
2025-07-16 06:05:59,381 - [BITVAVO] - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-07-16 06:05:59,381 - [BITVAVO] - root - INFO - [DEBUG]   - Best asset selected: SUI/EUR
2025-07-16 06:05:59,381 - [BITVAVO] - root - INFO - [DEBUG]   - Assets held: {'SUI/EUR': 1.0}
2025-07-16 06:05:59,381 - [BITVAVO] - root - INFO - [DEBUG]   - MTPI signal: 1
2025-07-16 06:05:59,381 - [BITVAVO] - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-07-16 06:05:59,381 - [BITVAVO] - root - ERROR - 🚨 ? SUI/EUR WAS SELECTED
2025-07-16 06:05:59,382 - [BITVAVO] - root - INFO - Executing single-asset strategy with best asset: SUI/EUR
2025-07-16 06:05:59,382 - [BITVAVO] - root - INFO - Executing strategy signal: best_asset=SUI/EUR, mtpi_signal=1, mode=live
2025-07-16 06:05:59,534 - [BITVAVO] - root - INFO - Incremented daily trade counter for SUI/EUR: 1/5
2025-07-16 06:05:59,535 - [BITVAVO] - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: SUI/EUR
2025-07-16 06:05:59,535 - [BITVAVO] - root - INFO - Attempting to enter position for SUI/EUR in live mode
2025-07-16 06:05:59,535 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Starting enter_position attempt
2025-07-16 06:05:59,535 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Trading mode: live
2025-07-16 06:05:59,535 - [BITVAVO] - root - INFO - TRADE ATTEMPT - SUI/EUR: Getting current market price...
2025-07-16 06:05:59,535 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Starting get_current_price
2025-07-16 06:05:59,535 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Exchange ID: bitvavo
2025-07-16 06:05:59,535 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Initializing exchange bitvavo
2025-07-16 06:05:59,565 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Exchange initialized successfully
2025-07-16 06:05:59,565 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Exchange markets not loaded, loading now...
2025-07-16 06:06:00,315 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Symbol found after loading markets
2025-07-16 06:06:00,317 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Attempting to fetch ticker...
2025-07-16 06:06:00,370 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker fetched successfully
2025-07-16 06:06:00,372 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker data: {'symbol': 'SUI/EUR', 'timestamp': 1752645957298, 'datetime': '2025-07-16T06:05:57.298Z', 'high': 3.5432, 'low': 3.288, 'bid': 3.4149, 'bidVolume': 230.92183342, 'ask': 3.4155, 'askVolume': 201.640591, 'vwap': 3.4245370310826404, 'open': 3.3641, 'close': 3.4166, 'last': 3.4166, 'previousClose': None, 'change': 0.0525, 'percentage': 1.560595701673553, 'average': 3.39035, 'baseVolume': 2762208.50535945, 'quoteVolume': 9459285.314174868, 'info': {'market': 'SUI-EUR', 'startTimestamp': '1752559557298', 'timestamp': '1752645957298', 'open': '3.3641', 'openTimestamp': '1752559563515', 'high': '3.5432', 'low': '3.288', 'last': '3.4166', 'closeTimestamp': '1752645956003', 'bid': '3.414900', 'bidSize': '230.92183342', 'ask': '3.415500', 'askSize': '201.64059100', 'volume': '2762208.50535945', 'volumeQuote': '9459285.314174868621'}, 'indexPrice': None, 'markPrice': None}
2025-07-16 06:06:00,372 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Last price: 3.4166
2025-07-16 06:06:00,373 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: get_current_price returned: 3.4166
2025-07-16 06:06:00,373 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price type: <class 'float'>
2025-07-16 06:06:00,373 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - not price: False
2025-07-16 06:06:00,373 - [BITVAVO] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - price <= 0: False
2025-07-16 06:06:00,373 - [BITVAVO] - root - INFO - TRADE ATTEMPT - SUI/EUR: Current price: 3.********
2025-07-16 06:06:00,438 - [BITVAVO] - root - INFO - Available balance for EUR: 95.********
2025-07-16 06:06:00,444 - [BITVAVO] - root - INFO - Reserved 0.358127 USDC for fees (rate: 0.25% with buffer)
2025-07-16 06:06:00,452 - [BITVAVO] - root - INFO - Loaded market info for 176 trading pairs
2025-07-16 06:06:00,452 - [BITVAVO] - root - INFO - Calculated position size for SUI/EUR: 27.******** (using 99.99% of 95.51, accounting for fees)
2025-07-16 06:06:00,453 - [BITVAVO] - root - INFO - Calculated position size: 27.******** SUI
2025-07-16 06:06:00,453 - [BITVAVO] - root - INFO - Entering position for SUI/EUR: 27.******** units at 3.******** (value: 94.******** EUR)
2025-07-16 06:06:00,453 - [BITVAVO] - root - INFO - Executing live market buy order for SUI/EUR
2025-07-16 06:06:00,502 - [BITVAVO] - root - INFO - Adjusted base amount for SUI/EUR: 27.******** -> 27.********
2025-07-16 06:06:00,627 - [BITVAVO] - root - INFO - Created market buy order: SUI/EUR, amount: 27.********485592, avg price: 3.4156
2025-07-16 06:06:00,627 - [BITVAVO] - root - INFO - Order fee: 0.************ EUR
2025-07-16 06:06:00,633 - [BITVAVO] - root - INFO - Filled amount: 27.********
2025-07-16 06:06:00,633 - [BITVAVO] - root - INFO - Filled amount: 27.******** SUI
2025-07-16 06:06:00,634 - [BITVAVO] - root - INFO - Order fee: 0.******** EUR
2025-07-16 06:06:00,634 - [BITVAVO] - root - INFO - Successfully entered position: SUI/EUR, amount: 27.********, price: 3.41560000
2025-07-16 06:06:00,635 - [BITVAVO] - root - INFO - Trade executed: BUY SUI/EUR, amount=27.********, price=3.41560000, filled=27.********
2025-07-16 06:06:00,635 - [BITVAVO] - root - INFO -   Fee: 0.******** EUR
2025-07-16 06:06:00,635 - [BITVAVO] - root - INFO - TRADE SUCCESS - SUI/EUR: Successfully updated current asset
2025-07-16 06:06:00,637 - [BITVAVO] - root - INFO - Trade executed: BUY SUI/EUR, amount=27.********, price=3.41560000, filled=27.********
2025-07-16 06:06:00,637 - [BITVAVO] - root - INFO -   Fee: 0.******** EUR
2025-07-16 06:06:00,637 - [BITVAVO] - root - INFO - Single-asset trade result logged to trade log file
2025-07-16 06:06:00,638 - [BITVAVO] - root - INFO - Trade executed: {'success': True, 'symbol': 'SUI/EUR', 'side': 'buy', 'amount': 27.********485592, 'price': 3.4156, 'order': {'info': {'orderId': '00000000-0000-056e-0100-0001b5fcf875', 'market': 'SUI-EUR', 'created': '1752645960606', 'updated': '1752645960606', 'status': 'filled', 'side': 'buy', 'orderType': 'market', 'selfTradePrevention': 'decrementAndCancel', 'visible': False, 'onHold': '0', 'onHoldCurrency': 'EUR', 'fills': [{'id': '00000000-0000-056e-0000-00000037e045', 'timestamp': '1752645960606', 'amount': '27.********', 'price': '3.4156', 'taker': True, 'fee': '0.************', 'feeCurrency': 'EUR', 'settled': True}], 'feePaid': '0.************', 'feeCurrency': 'EUR', 'operatorId': '1001', 'disableMarketProtection': False, 'amount': '27.********', 'amountRemaining': '0', 'filledAmount': '27.********', 'filledAmountQuote': '94.165708321072'}, 'id': '00000000-0000-056e-0100-0001b5fcf875', 'clientOrderId': None, 'timestamp': 1752645960606, 'datetime': '2025-07-16T06:06:00.606Z', 'lastTradeTimestamp': 1752645960606, 'symbol': 'SUI/EUR', 'type': 'market', 'timeInForce': 'IOC', 'postOnly': None, 'side': 'buy', 'price': 3.4156, 'triggerPrice': None, 'amount': 27.********, 'cost': 94.165708321072, 'average': 3.4156, 'filled': 27.********, 'remaining': 0.0, 'status': 'closed', 'fee': {'cost': 0.************, 'currency': 'EUR'}, 'trades': [{'info': {'id': '00000000-0000-056e-0000-00000037e045', 'timestamp': '1752645960606', 'amount': '27.********', 'price': '3.4156', 'taker': True, 'fee': '0.************', 'feeCurrency': 'EUR', 'settled': True}, 'id': '00000000-0000-056e-0000-00000037e045', 'symbol': 'SUI/EUR', 'timestamp': 1752645960606, 'datetime': '2025-07-16T06:06:00.606Z', 'order': None, 'type': None, 'side': None, 'takerOrMaker': 'taker', 'price': 3.4156, 'amount': 27.********, 'cost': 94.165708321072, 'fee': {'currency': 'EUR', 'cost': 0.************}, 'fees': [{'currency': 'EUR', 'cost': 0.************}]}], 'fees': [{'currency': 'EUR', 'cost': 0.************}], 'lastUpdateTimestamp': None, 'reduceOnly': None, 'stopPrice': None, 'takeProfitPrice': None, 'stopLossPrice': None, 'average_price': 3.4156}, 'filled_amount': 27.********, 'fee': {'cost': 0.************, 'currency': 'EUR', 'rate': 0.001}, 'quote_currency': 'EUR', 'base_currency': 'SUI', 'timestamp': '2025-07-16T06:06:00.634299'}
2025-07-16 06:06:01,647 - [BITVAVO] - root - WARNING - Failed to send with Markdown formatting: Pool timeout: All connections in the connection pool are occupied. Request was *not* sent to Telegram. Consider adjusting the connection pool size or the pool timeout.
2025-07-16 06:06:02,651 - [BITVAVO] - root - ERROR - Failed to send notification: Pool timeout: All connections in the connection pool are occupied. Request was *not* sent to Telegram. Consider adjusting the connection pool size or the pool timeout.
2025-07-16 06:06:02,656 - [BITVAVO] - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-07-16 06:06:02,656 - [BITVAVO] - root - INFO - Asset scores (sorted by score):
2025-07-16 06:06:02,656 - [BITVAVO] - root - INFO -   SUI/EUR: score=13.0, status=SELECTED, weight=1.00
2025-07-16 06:06:02,656 - [BITVAVO] - root - INFO -   XRP/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:02,656 - [BITVAVO] - root - INFO -   ADA/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:02,656 - [BITVAVO] - root - INFO -   ETH/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:02,656 - [BITVAVO] - root - INFO -   PEPE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:02,656 - [BITVAVO] - root - INFO -   LINK/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:02,656 - [BITVAVO] - root - INFO -   AAVE/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:02,657 - [BITVAVO] - root - INFO -   AVAX/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:02,657 - [BITVAVO] - root - INFO -   DOGE/EUR: score=5.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:02,657 - [BITVAVO] - root - INFO -   DOT/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:02,657 - [BITVAVO] - root - INFO -   BTC/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:02,657 - [BITVAVO] - root - INFO -   SOL/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:02,657 - [BITVAVO] - root - INFO -   TRX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:02,657 - [BITVAVO] - root - INFO -   BNB/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:02,657 - [BITVAVO] - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-07-16 06:06:02,657 - [BITVAVO] - root - INFO - Extracted asset scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 06:06:03,664 - [BITVAVO] - root - WARNING - Failed to send with Markdown formatting: Pool timeout: All connections in the connection pool are occupied. Request was *not* sent to Telegram. Consider adjusting the connection pool size or the pool timeout.
2025-07-16 06:06:04,672 - [BITVAVO] - root - ERROR - Failed to send notification: Pool timeout: All connections in the connection pool are occupied. Request was *not* sent to Telegram. Consider adjusting the connection pool size or the pool timeout.
2025-07-16 06:06:04,675 - [BITVAVO] - root - INFO - Strategy execution completed successfully in 72.41 seconds
2025-07-16 06:06:04,682 - [BITVAVO] - root - INFO - Saved recovery state to data/state/recovery_state.json
2025-07-16 12:00:05,730 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Starting get_current_price
2025-07-16 12:00:05,732 - [BITVAVO] - root - INFO - [DEBUG] PRICE - SUI/EUR: Exchange ID: bitvavo
2025-07-16 12:00:05,733 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Using existing exchange instance
2025-07-16 12:00:05,733 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Symbol found in exchange markets
2025-07-16 12:00:05,733 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Attempting to fetch ticker...
2025-07-16 12:00:05,856 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker fetched successfully
2025-07-16 12:00:05,856 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker data: {'symbol': 'SUI/EUR', 'timestamp': 1752667202297, 'datetime': '2025-07-16T12:00:02.297Z', 'high': 3.5432, 'low': 3.3303, 'bid': 3.454, 'bidVolume': 745.735, 'ask': 3.4544, 'askVolume': 227.03793092, 'vwap': 3.446955173334257, 'open': 3.4101, 'close': 3.4552, 'last': 3.4552, 'previousClose': None, 'change': 0.0451, 'percentage': 1.3225418609424944, 'average': 3.43265, 'baseVolume': 2788879.4784873, 'quoteVolume': 9613142.546177544, 'info': {'market': 'SUI-EUR', 'startTimestamp': '1752580802297', 'timestamp': '1752667202297', 'open': '3.4101', 'openTimestamp': '1752580844967', 'high': '3.5432', 'low': '3.3303', 'last': '3.4552', 'closeTimestamp': '1752667194916', 'bid': '3.454000', 'bidSize': '745.73500000', 'ask': '3.454400', 'askSize': '227.03793092', 'volume': '2788879.4784873', 'volumeQuote': '9613142.546177543274'}, 'indexPrice': None, 'markPrice': None}
2025-07-16 12:00:05,857 - [BITVAVO] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Last price: 3.4552
2025-07-16 12:00:06,867 - [BITVAVO] - root - WARNING - Failed to send with Markdown formatting: Pool timeout: All connections in the connection pool are occupied. Request was *not* sent to Telegram. Consider adjusting the connection pool size or the pool timeout.
2025-07-16 12:00:07,873 - [BITVAVO] - root - ERROR - Failed to send notification: Pool timeout: All connections in the connection pool are occupied. Request was *not* sent to Telegram. Consider adjusting the connection pool size or the pool timeout.
2025-07-16 12:00:07,873 - [BITVAVO] - root - INFO - Status update sent
