2025-07-16 00:03:54,353 - [<PERSON><PERSON><PERSON><PERSON>] - root - INFO - [MOMENTUM DEBUG] DOT/USDT: 0.0 (filtered, from 2025-07-12 00:00:00+00:00)
2025-07-16 00:03:54,353 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] Retrieved filtered previous_scores: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 3.0, 'SUI/USDT': 10.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 9.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-16 00:03:54,353 - [<PERSON><PERSON>KE<PERSON>] - root - INFO - [MOMENTUM DEBUG] Current filtered_scores will be: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 00:03:54,353 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 00:03:54,353 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 00:03:54,353 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=momentum, previous_scores=True
2025-07-16 00:03:54,353 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] FUNCTION CALLED! find_top_n_assets_with_momentum for n=1
2025-07-16 00:03:54,353 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] Current scores: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 00:03:54,353 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] Previous scores: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 3.0, 'SUI/USDT': 10.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 9.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-16 00:03:54,354 - [KRAKEN] - root - INFO - [MOMENTUM] Selected top 1 assets: ['XRP/USDT']
2025-07-16 00:03:54,354 - [KRAKEN] - root - INFO - [DEBUG] Momentum selection returned: ['XRP/USDT']
2025-07-16 00:03:54,354 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=momentum, current_holdings=True, filtered_scores=True
2025-07-16 00:03:54,355 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-14 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 12.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 00:03:54,355 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] Today: 2025-07-15 00:00:00+00:00, Yesterday: 2025-07-14 00:00:00+00:00, i: 215, start_idx: 60, prev_score_date: 2025-07-13 00:00:00+00:00
2025-07-16 00:03:54,355 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-13 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 00:03:54,356 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] ETH/USDT: 9.0 (filtered, from 2025-07-13 00:00:00+00:00)
2025-07-16 00:03:54,356 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] BTC/USDT: 4.0 (filtered, from 2025-07-13 00:00:00+00:00)
2025-07-16 00:03:54,356 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] SOL/USDT: 2.0 (filtered, from 2025-07-13 00:00:00+00:00)
2025-07-16 00:03:54,356 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] SUI/USDT: 9.0 (filtered, from 2025-07-13 00:00:00+00:00)
2025-07-16 00:03:54,356 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] XRP/USDT: 13.0 (filtered, from 2025-07-13 00:00:00+00:00)
2025-07-16 00:03:54,356 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] AAVE/USDT: 5.0 (filtered, from 2025-07-13 00:00:00+00:00)
2025-07-16 00:03:54,356 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] AVAX/USDT: 3.0 (filtered, from 2025-07-13 00:00:00+00:00)
2025-07-16 00:03:54,356 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] ADA/USDT: 10.0 (filtered, from 2025-07-13 00:00:00+00:00)
2025-07-16 00:03:54,356 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] LINK/USDT: 7.0 (filtered, from 2025-07-13 00:00:00+00:00)
2025-07-16 00:03:54,356 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] TRX/USDT: 1.0 (filtered, from 2025-07-13 00:00:00+00:00)
2025-07-16 00:03:54,356 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] PEPE/USDT: 9.0 (filtered, from 2025-07-13 00:00:00+00:00)
2025-07-16 00:03:54,356 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] DOGE/USDT: 8.0 (filtered, from 2025-07-13 00:00:00+00:00)
2025-07-16 00:03:54,356 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] BNB/USDT: 0.0 (filtered, from 2025-07-13 00:00:00+00:00)
2025-07-16 00:03:54,356 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] DOT/USDT: 2.0 (filtered, from 2025-07-13 00:00:00+00:00)
2025-07-16 00:03:54,356 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] Retrieved filtered previous_scores: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 00:03:54,356 - [KRAKEN] - root - INFO - [MOMENTUM DEBUG] Current filtered_scores will be: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 12.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 00:03:54,357 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 00:03:54,357 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 00:03:54,357 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=momentum, previous_scores=True
2025-07-16 00:03:54,357 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] FUNCTION CALLED! find_top_n_assets_with_momentum for n=1
2025-07-16 00:03:54,357 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] Current scores: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 12.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 00:03:54,357 - [KRAKEN] - root - ERROR - 🚨 [MOMENTUM] Previous scores: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 00:03:54,357 - [KRAKEN] - root - INFO - [MOMENTUM] Selected top 1 assets: ['XRP/USDT']
2025-07-16 00:03:54,357 - [KRAKEN] - root - INFO - [DEBUG] Momentum selection returned: ['XRP/USDT']
2025-07-16 00:03:54,357 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=momentum, current_holdings=True, filtered_scores=True
2025-07-16 00:03:54,424 - [KRAKEN] - root - INFO - Entry trade at 2025-04-23 00:00:00+00:00: PEPE/USDT
2025-07-16 00:03:54,424 - [KRAKEN] - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: PEPE/USDT -> SUI/USDT
2025-07-16 00:03:54,424 - [KRAKEN] - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-07-16 00:03:54,425 - [KRAKEN] - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-16 00:03:54,425 - [KRAKEN] - root - INFO - Swap trade at 2025-05-22 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-07-16 00:03:54,425 - [KRAKEN] - root - INFO - Swap trade at 2025-05-25 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-16 00:03:54,426 - [KRAKEN] - root - INFO - Exit trade at 2025-06-21 00:00:00+00:00 from AAVE/USDT
2025-07-16 00:03:54,426 - [KRAKEN] - root - INFO - Entry trade at 2025-07-11 00:00:00+00:00: SUI/USDT
2025-07-16 00:03:54,426 - [KRAKEN] - root - INFO - Swap trade at 2025-07-12 00:00:00+00:00: SUI/USDT -> XRP/USDT
2025-07-16 00:03:54,426 - [KRAKEN] - root - INFO - Total trades: 9 (Entries: 2, Exits: 1, Swaps: 6)
2025-07-16 00:03:54,428 - [KRAKEN] - root - INFO - Strategy execution completed in 0s
2025-07-16 00:03:54,428 - [KRAKEN] - root - INFO - DEBUG: self.elapsed_time = 0.7751107215881348 seconds
2025-07-16 00:03:54,441 - [KRAKEN] - root - INFO - Saved allocation history to allocation_history_1d_1d_with_mtpi_no_rebal_independent_momentum_2025-02-10.csv
2025-07-16 00:03:54,441 - [KRAKEN] - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-07-16 00:03:54,442 - [KRAKEN] - root - INFO - Assets included in buy-and-hold comparison:
2025-07-16 00:03:54,442 - [KRAKEN] - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-07-16 00:03:54,442 - [KRAKEN] - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-07-16 00:03:54,442 - [KRAKEN] - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-07-16 00:03:54,442 - [KRAKEN] - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-07-16 00:03:54,442 - [KRAKEN] - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-07-16 00:03:54,442 - [KRAKEN] - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-07-16 00:03:54,442 - [KRAKEN] - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-07-16 00:03:54,442 - [KRAKEN] - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-07-16 00:03:54,442 - [KRAKEN] - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-07-16 00:03:54,442 - [KRAKEN] - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-07-16 00:03:54,443 - [KRAKEN] - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-07-16 00:03:54,443 - [KRAKEN] - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-07-16 00:03:54,443 - [KRAKEN] - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-07-16 00:03:54,443 - [KRAKEN] - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-07-16 00:03:54,443 - [KRAKEN] - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-07-16 00:03:54,445 - [KRAKEN] - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 00:03:54,447 - [KRAKEN] - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 00:03:54,449 - [KRAKEN] - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 00:03:54,451 - [KRAKEN] - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 00:03:54,453 - [KRAKEN] - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 00:03:54,455 - [KRAKEN] - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 00:03:54,457 - [KRAKEN] - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 00:03:54,459 - [KRAKEN] - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 00:03:54,461 - [KRAKEN] - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 00:03:54,463 - [KRAKEN] - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 00:03:54,465 - [KRAKEN] - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 00:03:54,467 - [KRAKEN] - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 00:03:54,469 - [KRAKEN] - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 00:03:54,471 - [KRAKEN] - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 00:03:54,473 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 216 points
2025-07-16 00:03:54,474 - [KRAKEN] - root - INFO - ETH/USDT B&H total return: 17.91%
2025-07-16 00:03:54,477 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 216 points
2025-07-16 00:03:54,477 - [KRAKEN] - root - INFO - BTC/USDT B&H total return: 20.86%
2025-07-16 00:03:54,480 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 216 points
2025-07-16 00:03:54,480 - [KRAKEN] - root - INFO - SOL/USDT B&H total return: -18.20%
2025-07-16 00:03:54,484 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 216 points
2025-07-16 00:03:54,484 - [KRAKEN] - root - INFO - SUI/USDT B&H total return: 27.54%
2025-07-16 00:03:54,487 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 216 points
2025-07-16 00:03:54,488 - [KRAKEN] - root - INFO - XRP/USDT B&H total return: 20.44%
2025-07-16 00:03:54,491 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 216 points
2025-07-16 00:03:54,491 - [KRAKEN] - root - INFO - AAVE/USDT B&H total return: 29.65%
2025-07-16 00:03:54,494 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 216 points
2025-07-16 00:03:54,494 - [KRAKEN] - root - INFO - AVAX/USDT B&H total return: -12.31%
2025-07-16 00:03:54,498 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 216 points
2025-07-16 00:03:54,498 - [KRAKEN] - root - INFO - ADA/USDT B&H total return: 4.84%
2025-07-16 00:03:54,501 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 216 points
2025-07-16 00:03:54,501 - [KRAKEN] - root - INFO - LINK/USDT B&H total return: -14.35%
2025-07-16 00:03:54,504 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 216 points
2025-07-16 00:03:54,505 - [KRAKEN] - root - INFO - TRX/USDT B&H total return: 22.06%
2025-07-16 00:03:54,507 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 216 points
2025-07-16 00:03:54,508 - [KRAKEN] - root - INFO - PEPE/USDT B&H total return: 34.10%
2025-07-16 00:03:54,511 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 216 points
2025-07-16 00:03:54,511 - [KRAKEN] - root - INFO - DOGE/USDT B&H total return: -22.01%
2025-07-16 00:03:54,514 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 216 points
2025-07-16 00:03:54,515 - [KRAKEN] - root - INFO - BNB/USDT B&H total return: 11.52%
2025-07-16 00:03:54,519 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 216 points
2025-07-16 00:03:54,519 - [KRAKEN] - root - INFO - DOT/USDT B&H total return: -15.73%
2025-07-16 00:03:54,522 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 00:03:54,537 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 00:03:54,548 - [KRAKEN] - root - INFO - Using colored segments for single-asset strategy visualization
2025-07-16 00:03:54,693 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 00:03:54,706 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 00:03:55,306 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:03:57,175 - [KRAKEN] - root - INFO - Added ETH/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:57,176 - [KRAKEN] - root - INFO - Added BTC/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:57,176 - [KRAKEN] - root - INFO - Added SOL/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:57,176 - [KRAKEN] - root - INFO - Added SUI/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:57,176 - [KRAKEN] - root - INFO - Added XRP/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:57,176 - [KRAKEN] - root - INFO - Added AAVE/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:57,176 - [KRAKEN] - root - INFO - Added AVAX/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:57,176 - [KRAKEN] - root - INFO - Added ADA/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:57,176 - [KRAKEN] - root - INFO - Added LINK/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:57,176 - [KRAKEN] - root - INFO - Added TRX/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:57,176 - [KRAKEN] - root - INFO - Added PEPE/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:57,176 - [KRAKEN] - root - INFO - Added DOGE/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:57,177 - [KRAKEN] - root - INFO - Added BNB/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:57,177 - [KRAKEN] - root - INFO - Added DOT/USDT buy-and-hold curve with 216 points
2025-07-16 00:03:57,177 - [KRAKEN] - root - INFO - Added 14 buy-and-hold curves to results
2025-07-16 00:03:57,177 - [KRAKEN] - root - INFO -   - ETH/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:57,177 - [KRAKEN] - root - INFO -   - BTC/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:57,177 - [KRAKEN] - root - INFO -   - SOL/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:57,177 - [KRAKEN] - root - INFO -   - SUI/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:57,177 - [KRAKEN] - root - INFO -   - XRP/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:57,178 - [KRAKEN] - root - INFO -   - AAVE/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:57,178 - [KRAKEN] - root - INFO -   - AVAX/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:57,178 - [KRAKEN] - root - INFO -   - ADA/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:57,178 - [KRAKEN] - root - INFO -   - LINK/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:57,178 - [KRAKEN] - root - INFO -   - TRX/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:57,178 - [KRAKEN] - root - INFO -   - PEPE/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:57,178 - [KRAKEN] - root - INFO -   - DOGE/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:57,178 - [KRAKEN] - root - INFO -   - BNB/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:57,178 - [KRAKEN] - root - INFO -   - DOT/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:57,206 - [KRAKEN] - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-07-16 00:03:57,206 - [KRAKEN] - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-16 00:03:57,209 - [KRAKEN] - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-07-16 00:03:57,210 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 00:03:57,226 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 00:03:57,227 - [KRAKEN] - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 00:03:57,227 - [KRAKEN] - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 00:03:57,227 - [KRAKEN] - root - INFO - Combination method: consensus
2025-07-16 00:03:57,227 - [KRAKEN] - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-16 00:03:57,227 - [KRAKEN] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-16 00:03:57,247 - [KRAKEN] - root - INFO - Loaded 2157 rows of BTC/USDT data from cache (last updated: 2025-07-16)
2025-07-16 00:03:57,248 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 00:03:57,248 - [KRAKEN] - root - INFO - Loaded 2157 rows of BTC/USDT data from cache (after filtering).
2025-07-16 00:03:57,248 - [KRAKEN] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-16 00:03:57,248 - [KRAKEN] - root - INFO - Fetched BTC data: 2157 candles from 2019-08-20 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 00:03:57,249 - [KRAKEN] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-16 00:03:57,573 - [KRAKEN] - root - INFO - Generated PGO Score signals: {-1: np.int64(996), 0: np.int64(34), 1: np.int64(1127)}
2025-07-16 00:03:57,573 - [KRAKEN] - root - INFO - PGO signal: 1
2025-07-16 00:03:57,573 - [KRAKEN] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-16 00:03:57,703 - [KRAKEN] - root - INFO - Generated BB Score signals: {-1: np.int64(993), 0: np.int64(33), 1: np.int64(1131)}
2025-07-16 00:03:57,703 - [KRAKEN] - root - INFO - Bollinger Bands signal: 1
2025-07-16 00:04:03,819 - [KRAKEN] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-16 00:04:03,819 - [KRAKEN] - root - INFO - DWMA Score signal: 1
2025-07-16 00:04:04,446 - [KRAKEN] - root - INFO - Generated DEMA Supertrend signals
2025-07-16 00:04:04,446 - [KRAKEN] - root - INFO - Signal distribution: {-1: np.int64(1268), 0: np.int64(1), 1: np.int64(888)}
2025-07-16 00:04:04,447 - [KRAKEN] - root - INFO - Generated DEMA Super Score signals
2025-07-16 00:04:04,447 - [KRAKEN] - root - INFO - DEMA Super Score signal: 1
2025-07-16 00:04:05,651 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:04:06,033 - [KRAKEN] - root - INFO - Generated DPSD signals
2025-07-16 00:04:06,033 - [KRAKEN] - root - INFO - Signal distribution: {-1: np.int64(1087), 0: np.int64(87), 1: np.int64(983)}
2025-07-16 00:04:06,033 - [KRAKEN] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-16 00:04:06,034 - [KRAKEN] - root - INFO - DPSD Score signal: 1
2025-07-16 00:04:06,133 - [KRAKEN] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-16 00:04:06,134 - [KRAKEN] - root - INFO - Generated AAD Score signals using SMA method
2025-07-16 00:04:06,134 - [KRAKEN] - root - INFO - AAD Score signal: 1
2025-07-16 00:04:06,938 - [KRAKEN] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-16 00:04:06,941 - [KRAKEN] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-16 00:04:06,942 - [KRAKEN] - root - INFO - Dynamic EMA Score signal: 1
2025-07-16 00:04:08,321 - [KRAKEN] - root - INFO - Quantile DEMA Score signal: 1
2025-07-16 00:04:08,322 - [KRAKEN] - root - INFO - Individual signals: {'pgo': 1, 'bollinger_bands': 1, 'dwma_score': 1, 'dema_super_score': 1, 'dpsd_score': 1, 'aad_score': 1, 'dynamic_ema_score': 1, 'quantile_dema_score': 1}
2025-07-16 00:04:08,322 - [KRAKEN] - root - INFO - Combined MTPI signal (consensus): 1
2025-07-16 00:04:08,322 - [KRAKEN] - root - INFO - MTPI Score: 1.000000
2025-07-16 00:04:08,322 - [KRAKEN] - root - INFO - Added current MTPI score to results: 1.000000 (using 1d timeframe)
2025-07-16 00:04:08,326 - [KRAKEN] - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-07-16 00:04:08,327 - [KRAKEN] - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 13.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 00:04:08,327 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 13.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 00:04:08,327 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-16 00:04:08,328 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-16 00:04:08,328 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 9.0)
2025-07-16 00:04:08,328 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 3.0)
2025-07-16 00:04:08,328 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 2.0)
2025-07-16 00:04:08,328 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 13.0)
2025-07-16 00:04:08,328 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 12.0)
2025-07-16 00:04:08,328 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 6.0)
2025-07-16 00:04:08,328 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 6.0)
2025-07-16 00:04:08,328 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 10.0)
2025-07-16 00:04:08,328 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 7.0)
2025-07-16 00:04:08,328 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 1.0)
2025-07-16 00:04:08,328 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 9.0)
2025-07-16 00:04:08,328 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 5.0)
2025-07-16 00:04:08,328 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 0.0)
2025-07-16 00:04:08,328 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 4.0)
2025-07-16 00:04:08,329 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 00:04:08,329 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 00:04:08,329 - [KRAKEN] - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 00:04:08,346 - [KRAKEN] - root - INFO - Appended metrics to existing file: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250709_run_20250712_085027.csv
2025-07-16 00:04:08,347 - [KRAKEN] - root - INFO - Saved performance metrics to CSV: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250709_run_20250712_085027.csv
2025-07-16 00:04:08,347 - [KRAKEN] - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-07-16 00:04:08,347 - [KRAKEN] - root - INFO - Results type: <class 'dict'>
2025-07-16 00:04:08,347 - [KRAKEN] - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-07-16 00:04:08,347 - [KRAKEN] - root - INFO - Success flag set to: True
2025-07-16 00:04:08,347 - [KRAKEN] - root - INFO - Message set to: Strategy calculation completed successfully
2025-07-16 00:04:08,347 - [KRAKEN] - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 216 entries
2025-07-16 00:04:08,347 - [KRAKEN] - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-07-16 00:04:08,347 - [KRAKEN] - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 216 entries
2025-07-16 00:04:08,347 - [KRAKEN] - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 276 entries
2025-07-16 00:04:08,347 - [KRAKEN] - root - INFO -   - mtpi_score: <class 'float'>
2025-07-16 00:04:08,347 - [KRAKEN] - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 216 entries
2025-07-16 00:04:08,347 - [KRAKEN] - root - INFO -   - performance_metrics: dict with 3 entries
2025-07-16 00:04:08,348 - [KRAKEN] - root - INFO -   - metrics_file: <class 'str'>
2025-07-16 00:04:08,348 - [KRAKEN] - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-07-16 00:04:08,348 - [KRAKEN] - root - INFO -   - success: <class 'bool'>
2025-07-16 00:04:08,348 - [KRAKEN] - root - INFO -   - message: <class 'str'>
2025-07-16 00:04:08,348 - [KRAKEN] - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-07-16 00:04:08,348 - [KRAKEN] - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: XRP/EUR
2025-07-16 00:04:08,349 - [KRAKEN] - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-07-11 00:00:00+00:00    SUI/EUR
2025-07-12 00:00:00+00:00    XRP/EUR
2025-07-13 00:00:00+00:00    XRP/EUR
2025-07-14 00:00:00+00:00    XRP/EUR
2025-07-15 00:00:00+00:00    XRP/EUR
dtype: object
2025-07-16 00:04:08,349 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 00:04:08,349 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 00:04:08,349 - [KRAKEN] - root - INFO - [DEBUG] TIE-BREAKING - Strategy: momentum
2025-07-16 00:04:08,349 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-07-16 00:04:08,350 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 00:04:08,350 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 00:04:08,350 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 13.0
2025-07-16 00:04:08,350 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 13.0: ['SUI/EUR']
2025-07-16 00:04:08,350 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: SUI/EUR
2025-07-16 00:04:08,350 - [KRAKEN] - root - ERROR - [DEBUG] SELECTED BEST ASSET: SUI/EUR (score: 13.0)
2025-07-16 00:04:08,350 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: SUI/EUR (MTPI signal: 1)
2025-07-16 00:04:08,350 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-07-16 00:04:08,350 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['SUI/EUR']
2025-07-16 00:04:08,350 - [KRAKEN] - root - ERROR - [DEBUG] NO TIE - UPDATING SELECTION: XRP/EUR -> SUI/EUR
2025-07-16 00:04:08,350 - [KRAKEN] - root - ERROR - [DEBUG] NO TIE - Single winner: SUI/EUR
2025-07-16 00:04:08,377 - [KRAKEN] - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-07-16 00:04:08,378 - [KRAKEN] - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 00:04:08,378 - [KRAKEN] - root - INFO - Single asset strategy with best asset: SUI/EUR
2025-07-16 00:04:08,378 - [KRAKEN] - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-07-16 00:04:08,378 - [KRAKEN] - root - INFO - [DEBUG]   - Best asset selected: SUI/EUR
2025-07-16 00:04:08,378 - [KRAKEN] - root - INFO - [DEBUG]   - Assets held: {'SUI/EUR': 1.0}
2025-07-16 00:04:08,378 - [KRAKEN] - root - INFO - [DEBUG]   - MTPI signal: 1
2025-07-16 00:04:08,378 - [KRAKEN] - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-07-16 00:04:08,378 - [KRAKEN] - root - ERROR - 🚨 ? SUI/EUR WAS SELECTED
2025-07-16 00:04:08,459 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-07-16 00:04:08,463 - [KRAKEN] - root - INFO - Executing single-asset strategy with best asset: SUI/EUR
2025-07-16 00:04:08,463 - [KRAKEN] - root - INFO - Executing strategy signal: best_asset=SUI/EUR, mtpi_signal=1, mode=live
2025-07-16 00:04:08,463 - [KRAKEN] - root - INFO - Already holding the best asset: SUI/EUR. No trade needed.
2025-07-16 00:04:08,468 - [KRAKEN] - root - INFO - Trade executed: HOLD SUI/EUR, amount=0.00000000, price=0.00000000, filled=0.00000000
2025-07-16 00:04:08,471 - [KRAKEN] - root - INFO - Trade executed: UNKNOWN SUI/EUR, amount=0.00000000, price=0.00000000, filled=0.00000000
2025-07-16 00:04:08,471 - [KRAKEN] - root - INFO - Single-asset trade result logged to trade log file
2025-07-16 00:04:08,472 - [KRAKEN] - root - INFO - Trade executed: {'success': True, 'reason': 'Already holding best asset', 'symbol': 'SUI/EUR'}
2025-07-16 00:04:08,476 - [KRAKEN] - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-07-16 00:04:08,476 - [KRAKEN] - root - INFO - Asset scores (sorted by score):
2025-07-16 00:04:08,476 - [KRAKEN] - root - INFO -   SUI/EUR: score=13.0, status=SELECTED, weight=1.00
2025-07-16 00:04:08,476 - [KRAKEN] - root - INFO -   XRP/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:04:08,477 - [KRAKEN] - root - INFO -   ADA/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:04:08,477 - [KRAKEN] - root - INFO -   ETH/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:04:08,477 - [KRAKEN] - root - INFO -   PEPE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:04:08,477 - [KRAKEN] - root - INFO -   LINK/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:04:08,477 - [KRAKEN] - root - INFO -   AAVE/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:04:08,477 - [KRAKEN] - root - INFO -   AVAX/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:04:08,477 - [KRAKEN] - root - INFO -   DOGE/EUR: score=5.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:04:08,477 - [KRAKEN] - root - INFO -   DOT/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:04:08,477 - [KRAKEN] - root - INFO -   BTC/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:04:08,477 - [KRAKEN] - root - INFO -   SOL/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:04:08,477 - [KRAKEN] - root - INFO -   TRX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:04:08,477 - [KRAKEN] - root - INFO -   BNB/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-16 00:04:08,478 - [KRAKEN] - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-07-16 00:04:08,478 - [KRAKEN] - root - INFO - Extracted asset scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 00:04:08,515 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-07-16 00:04:08,516 - [KRAKEN] - root - INFO - Strategy execution completed successfully in 55.05 seconds
2025-07-16 00:04:08,518 - [KRAKEN] - root - INFO - Saved recovery state to data/state/recovery_state.json
2025-07-16 00:04:08,518 - [KRAKEN] - root - WARNING - Recovery from strategy_execution_failure failure was unsuccessful
2025-07-16 00:04:08,532 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 400 Bad Request"
2025-07-16 00:04:08,533 - [KRAKEN] - root - WARNING - Failed to send with Markdown formatting: Can't parse entities: can't find end of the entity starting at byte offset 137
2025-07-16 00:04:08,572 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-07-16 00:04:20,934 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:04:31,730 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:04:42,017 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:04:52,762 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:05:03,050 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:05:08,574 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:05:13,343 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:05:23,629 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:05:33,919 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:05:44,211 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:05:54,502 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:06:04,790 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:06:15,081 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:06:25,372 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:06:35,662 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:06:45,957 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:06:56,245 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:07:06,538 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:07:16,827 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:07:27,118 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:07:37,407 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:07:47,698 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:07:57,989 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:08:08,277 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:08:18,569 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:08:28,856 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:08:39,142 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:08:49,436 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:08:59,727 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:09:10,012 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:09:20,305 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:09:30,597 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:09:40,891 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 00:09:51,190 - [KRAKEN] - root - INFO - Currently in critical time window: After daily candle close (1d)
2025-07-16 06:04:34,490 - [KRAKEN] - root - INFO - Received signal 15, shutting down...
2025-07-16 06:04:44,497 - [KRAKEN] - root - INFO - Network watchdog stopped
2025-07-16 06:04:44,497 - [KRAKEN] - root - INFO - Network watchdog stopped
2025-07-16 06:04:44,497 - [KRAKEN] - root - INFO - Background service stopped
2025-07-16 06:04:44,566 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
