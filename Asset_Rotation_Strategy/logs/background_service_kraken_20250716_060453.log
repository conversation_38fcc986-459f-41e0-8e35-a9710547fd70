2025-07-16 06:04:53,262 - [KRAKEN] - root - INFO - Exchange-specific logging initialized for kraken
2025-07-16 06:04:53,733 - [KRAKEN] - root - INFO - Telegram command handlers registered
2025-07-16 06:04:53,734 - [KRAKEN] - root - INFO - Telegram bot polling started
2025-07-16 06:04:53,734 - [KRAKEN] - root - INFO - Telegram notifier initialized with notification level: standard
2025-07-16 06:04:53,734 - [KRAKEN] - root - INFO - Telegram notification channel initialized
2025-07-16 06:04:53,735 - [KRAKEN] - root - ERROR - Error in Telegram polling: set_wakeup_fd only works in main thread of the main interpreter
2025-07-16 06:04:53,737 - [KRAKEN] - root - INFO - Successfully loaded templates using utf-8 encoding
2025-07-16 06:04:53,737 - [KRAKEN] - root - WARNING - Continuing without Telegram bot polling. Notifications will still be sent.
2025-07-16 06:04:53,741 - [KRAKEN] - root - INFO - Loaded 26 templates from file
2025-07-16 06:04:53,741 - [KRAKEN] - root - INFO - Notification manager initialized with 1 channels
2025-07-16 06:04:53,741 - [KRAKEN] - root - INFO - Notification manager initialized
2025-07-16 06:04:53,741 - [KRAKEN] - root - INFO - Added critical time window: 23:55 for 10 minutes - Before daily candle close (1d)
2025-07-16 06:04:53,741 - [KRAKEN] - root - INFO - Added critical time window: 00:00 for 10 minutes - After daily candle close (1d)
2025-07-16 06:04:53,741 - [KRAKEN] - root - INFO - Set up critical time windows for 1d timeframe
2025-07-16 06:04:53,742 - [KRAKEN] - root - INFO - Network watchdog initialized with 10s check interval
2025-07-16 06:04:53,742 - [KRAKEN] - root - INFO - Loaded recovery state from data/state/recovery_state.json
2025-07-16 06:04:53,742 - [KRAKEN] - root - INFO - No state file found at /home/<USER>/asset_rotation_screener/Asset_Rotation_Strategy/data/state/data/state/background_service_20250627_214259.json.json
2025-07-16 06:04:53,742 - [KRAKEN] - root - INFO - Recovery manager initialized
2025-07-16 06:04:53,742 - [KRAKEN] - root - INFO - [DEBUG] TRADING EXECUTOR - Initializing with exchange: kraken
2025-07-16 06:04:53,742 - [KRAKEN] - root - INFO - [DEBUG] TRADING EXECUTOR - Trading config: {'enabled': True, 'exchange': 'kraken', 'initial_capital': 100, 'max_slippage_pct': 0.5, 'min_order_values': {'AAVE/EUR': 5.0, 'ADA/EUR': 2.0, 'AVAX/EUR': 4.0, 'BNB/EUR': 3.0, 'BTC/EUR': 3.5, 'DOGE/EUR': 5.0, 'DOT/EUR': 4.5, 'ETH/EUR': 7.5, 'LINK/EUR': 3.5, 'PEPE/EUR': 5.0, 'SOL/EUR': 4.5, 'SUI/EUR': 2.0, 'TRX/EUR': 3.0, 'XRP/EUR': 5.0, 'default': 5.0}, 'mode': 'live', 'order_type': 'market', 'position_size_pct': 99.99, 'risk_management': {'max_daily_trades': 5, 'max_open_positions': 20}, 'transaction_fee_rate': 0.004}
2025-07-16 06:04:53,742 - [KRAKEN] - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-07-16 06:04:53,769 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:04:53,769 - [KRAKEN] - root - INFO - Getting credentials for exchange_id: kraken
2025-07-16 06:04:53,771 - [KRAKEN] - root - INFO - Looking for environment variables: KRAKEN_API_KEY, KRAKEN_API_SECRET
2025-07-16 06:04:53,771 - [KRAKEN] - root - INFO - Loaded API key from environment variable KRAKEN_API_KEY
2025-07-16 06:04:53,771 - [KRAKEN] - root - INFO - Loaded API secret from environment variable KRAKEN_API_SECRET
2025-07-16 06:04:53,771 - [KRAKEN] - root - INFO - Getting credentials for exchange_id: kraken
2025-07-16 06:04:53,771 - [KRAKEN] - root - INFO - Looking for environment variables: KRAKEN_API_KEY, KRAKEN_API_SECRET
2025-07-16 06:04:53,772 - [KRAKEN] - root - INFO - Loaded API key from environment variable KRAKEN_API_KEY
2025-07-16 06:04:53,772 - [KRAKEN] - root - INFO - Loaded API secret from environment variable KRAKEN_API_SECRET
2025-07-16 06:04:53,772 - [KRAKEN] - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-07-16 06:04:53,804 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:04:55,836 - [KRAKEN] - root - INFO - Successfully loaded markets for kraken.
2025-07-16 06:04:57,887 - [KRAKEN] - root - INFO - Successfully connected to kraken exchange.
2025-07-16 06:04:57,887 - [KRAKEN] - root - INFO - Getting credentials for exchange_id: kraken
2025-07-16 06:04:57,887 - [KRAKEN] - root - INFO - Looking for environment variables: KRAKEN_API_KEY, KRAKEN_API_SECRET
2025-07-16 06:04:57,887 - [KRAKEN] - root - INFO - Loaded API key from environment variable KRAKEN_API_KEY
2025-07-16 06:04:57,887 - [KRAKEN] - root - INFO - Loaded API secret from environment variable KRAKEN_API_SECRET
2025-07-16 06:04:57,887 - [KRAKEN] - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-07-16 06:04:57,928 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:02,009 - [KRAKEN] - root - INFO - Successfully connected to kraken exchange.
2025-07-16 06:05:02,011 - [KRAKEN] - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-07-16 06:05:02,056 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:02,056 - [KRAKEN] - root - INFO - Trading executor initialized for kraken
2025-07-16 06:05:02,057 - [KRAKEN] - root - INFO - Trading mode: live
2025-07-16 06:05:02,057 - [KRAKEN] - root - INFO - Trading enabled: True
2025-07-16 06:05:02,057 - [KRAKEN] - root - INFO - Getting credentials for exchange_id: kraken
2025-07-16 06:05:02,057 - [KRAKEN] - root - INFO - Looking for environment variables: KRAKEN_API_KEY, KRAKEN_API_SECRET
2025-07-16 06:05:02,057 - [KRAKEN] - root - INFO - Loaded API key from environment variable KRAKEN_API_KEY
2025-07-16 06:05:02,057 - [KRAKEN] - root - INFO - Loaded API secret from environment variable KRAKEN_API_SECRET
2025-07-16 06:05:02,059 - [KRAKEN] - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-07-16 06:05:02,092 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:03,856 - [KRAKEN] - root - INFO - Successfully loaded markets for kraken.
2025-07-16 06:05:06,239 - [KRAKEN] - root - INFO - Successfully connected to kraken exchange.
2025-07-16 06:05:06,241 - [KRAKEN] - root - INFO - Notification manager passed to trading executor
2025-07-16 06:05:06,241 - [KRAKEN] - root - INFO - Trading enabled in live mode
2025-07-16 06:05:09,182 - [KRAKEN] - root - INFO - Connected to kraken, balance: 131.2308 EUR
2025-07-16 06:05:09,185 - [KRAKEN] - root - INFO - Generated run ID: 20250716_060509
2025-07-16 06:05:09,185 - [KRAKEN] - root - INFO - Ensured metrics directory exists: Performance_Metrics
2025-07-16 06:05:09,186 - [KRAKEN] - root - INFO - Background service initialized
2025-07-16 06:05:09,186 - [KRAKEN] - root - INFO - Network watchdog started
2025-07-16 06:05:09,186 - [KRAKEN] - root - INFO - Network watchdog started
2025-07-16 06:05:09,187 - [KRAKEN] - root - INFO - Schedule set up for 1d timeframe
2025-07-16 06:05:09,191 - [KRAKEN] - root - INFO - Executing strategy (run #1)...
2025-07-16 06:05:09,191 - [KRAKEN] - root - INFO - Resetting daily trade counters for this strategy execution
2025-07-16 06:05:09,191 - [KRAKEN] - root - INFO - No trades recorded today (Max: 5)
2025-07-16 06:05:09,191 - [KRAKEN] - root - INFO - Initialized daily trades counter for 2025-07-16
2025-07-16 06:05:09,191 - [KRAKEN] - root - INFO - Creating snapshot for candle timestamp: 20250716
2025-07-16 06:05:09,194 - [KRAKEN] - root - INFO - Background service started
2025-07-16 06:05:09,315 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-07-16 06:05:10,230 - [KRAKEN] - root - WARNING - Failed to send with Markdown formatting: Unknown error in HTTP implementation: RuntimeError('<asyncio.locks.Event object at 0x730937d23170 [unset]> is bound to a different event loop')
2025-07-16 06:05:10,306 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-07-16 06:05:10,309 - [KRAKEN] - root - INFO - Using trend method 'PGO For Loop' for strategy execution
2025-07-16 06:05:10,309 - [KRAKEN] - root - INFO - Using configured start date for 1d timeframe: 2025-02-10
2025-07-16 06:05:10,311 - [KRAKEN] - root - INFO - Using recent date for performance tracking: 2025-07-09
2025-07-16 06:05:10,312 - [KRAKEN] - root - INFO - Using real-time data fetching - only fetching new data since last cached timestamp
2025-07-16 06:05:10,416 - [KRAKEN] - root - INFO - Loaded 2157 rows of ETH/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:10,418 - [KRAKEN] - root - INFO - Last timestamp in cache for ETH/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,419 - [KRAKEN] - root - INFO - Expected last timestamp for ETH/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,420 - [KRAKEN] - root - INFO - Data is up to date for ETH/USDT
2025-07-16 06:05:10,424 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:10,468 - [KRAKEN] - root - INFO - Loaded 2157 rows of BTC/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:10,469 - [KRAKEN] - root - INFO - Last timestamp in cache for BTC/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,469 - [KRAKEN] - root - INFO - Expected last timestamp for BTC/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,469 - [KRAKEN] - root - INFO - Data is up to date for BTC/USDT
2025-07-16 06:05:10,472 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:10,507 - [KRAKEN] - root - INFO - Loaded 1800 rows of SOL/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:10,507 - [KRAKEN] - root - INFO - Last timestamp in cache for SOL/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,508 - [KRAKEN] - root - INFO - Expected last timestamp for SOL/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,508 - [KRAKEN] - root - INFO - Data is up to date for SOL/USDT
2025-07-16 06:05:10,511 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:10,532 - [KRAKEN] - root - INFO - Loaded 805 rows of SUI/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:10,535 - [KRAKEN] - root - INFO - Last timestamp in cache for SUI/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,535 - [KRAKEN] - root - INFO - Expected last timestamp for SUI/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,535 - [KRAKEN] - root - INFO - Data is up to date for SUI/USDT
2025-07-16 06:05:10,536 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:10,572 - [KRAKEN] - root - INFO - Loaded 2157 rows of XRP/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:10,575 - [KRAKEN] - root - INFO - Last timestamp in cache for XRP/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,575 - [KRAKEN] - root - INFO - Expected last timestamp for XRP/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,575 - [KRAKEN] - root - INFO - Data is up to date for XRP/USDT
2025-07-16 06:05:10,578 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:10,611 - [KRAKEN] - root - INFO - Loaded 1735 rows of AAVE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:10,612 - [KRAKEN] - root - INFO - Last timestamp in cache for AAVE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,612 - [KRAKEN] - root - INFO - Expected last timestamp for AAVE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,613 - [KRAKEN] - root - INFO - Data is up to date for AAVE/USDT
2025-07-16 06:05:10,614 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:10,648 - [KRAKEN] - root - INFO - Loaded 1758 rows of AVAX/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:10,650 - [KRAKEN] - root - INFO - Last timestamp in cache for AVAX/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,651 - [KRAKEN] - root - INFO - Expected last timestamp for AVAX/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,651 - [KRAKEN] - root - INFO - Data is up to date for AVAX/USDT
2025-07-16 06:05:10,652 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:10,691 - [KRAKEN] - root - INFO - Loaded 2157 rows of ADA/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:10,693 - [KRAKEN] - root - INFO - Last timestamp in cache for ADA/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,694 - [KRAKEN] - root - INFO - Expected last timestamp for ADA/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,694 - [KRAKEN] - root - INFO - Data is up to date for ADA/USDT
2025-07-16 06:05:10,698 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:10,757 - [KRAKEN] - root - INFO - Loaded 2157 rows of LINK/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:10,758 - [KRAKEN] - root - INFO - Last timestamp in cache for LINK/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,758 - [KRAKEN] - root - INFO - Expected last timestamp for LINK/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,759 - [KRAKEN] - root - INFO - Data is up to date for LINK/USDT
2025-07-16 06:05:10,762 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:10,819 - [KRAKEN] - root - INFO - Loaded 2157 rows of TRX/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:10,822 - [KRAKEN] - root - INFO - Last timestamp in cache for TRX/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,822 - [KRAKEN] - root - INFO - Expected last timestamp for TRX/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,822 - [KRAKEN] - root - INFO - Data is up to date for TRX/USDT
2025-07-16 06:05:10,826 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:10,859 - [KRAKEN] - root - INFO - Loaded 803 rows of PEPE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:10,861 - [KRAKEN] - root - INFO - Last timestamp in cache for PEPE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,862 - [KRAKEN] - root - INFO - Expected last timestamp for PEPE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,862 - [KRAKEN] - root - INFO - Data is up to date for PEPE/USDT
2025-07-16 06:05:10,865 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:10,930 - [KRAKEN] - root - INFO - Loaded 2157 rows of DOGE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:10,930 - [KRAKEN] - root - INFO - Last timestamp in cache for DOGE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,931 - [KRAKEN] - root - INFO - Expected last timestamp for DOGE/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,933 - [KRAKEN] - root - INFO - Data is up to date for DOGE/USDT
2025-07-16 06:05:10,935 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:10,995 - [KRAKEN] - root - INFO - Loaded 2157 rows of BNB/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:10,997 - [KRAKEN] - root - INFO - Last timestamp in cache for BNB/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,998 - [KRAKEN] - root - INFO - Expected last timestamp for BNB/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:10,998 - [KRAKEN] - root - INFO - Data is up to date for BNB/USDT
2025-07-16 06:05:11,002 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:11,054 - [KRAKEN] - root - INFO - Loaded 1793 rows of DOT/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:11,055 - [KRAKEN] - root - INFO - Last timestamp in cache for DOT/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:11,058 - [KRAKEN] - root - INFO - Expected last timestamp for DOT/USDT: 2025-07-15 00:00:00+00:00
2025-07-16 06:05:11,058 - [KRAKEN] - root - INFO - Data is up to date for DOT/USDT
2025-07-16 06:05:11,061 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:11,063 - [KRAKEN] - root - INFO - Using 14 trend assets (USDT) for analysis and 14 trading assets (EUR) for execution
2025-07-16 06:05:11,065 - [KRAKEN] - root - INFO - MTPI Multi-Indicator Configuration:
2025-07-16 06:05:11,065 - [KRAKEN] - root - INFO -   - Number of indicators: 8
2025-07-16 06:05:11,065 - [KRAKEN] - root - INFO -   - Enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 06:05:11,066 - [KRAKEN] - root - INFO -   - Combination method: consensus
2025-07-16 06:05:11,066 - [KRAKEN] - root - INFO -   - Long threshold: 0.1
2025-07-16 06:05:11,066 - [KRAKEN] - root - INFO -   - Short threshold: -0.1
2025-07-16 06:05:11,066 - [KRAKEN] - root - ERROR - [DEBUG] BACKGROUND SERVICE - trend_assets order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-16 06:05:11,066 - [KRAKEN] - root - ERROR - [DEBUG] BACKGROUND SERVICE - trading_assets order: ['BTC/EUR', 'ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:05:11,066 - [KRAKEN] - root - ERROR - [DEBUG] BACKGROUND SERVICE - BTC position in trend_assets: 2
2025-07-16 06:05:11,066 - [KRAKEN] - root - ERROR - [DEBUG] BACKGROUND SERVICE - TRX position in trend_assets: 10
2025-07-16 06:05:11,066 - [KRAKEN] - root - INFO - === RUNNING STRATEGY FOR WEB USING TEST_ALLOCATION ===
2025-07-16 06:05:11,066 - [KRAKEN] - root - INFO - Parameters: use_mtpi_signal=True, mtpi_indicator_type=PGO
2025-07-16 06:05:11,066 - [KRAKEN] - root - INFO - mtpi_timeframe=1d, mtpi_pgo_length=35
2025-07-16 06:05:11,066 - [KRAKEN] - root - INFO - mtpi_upper_threshold=1.1, mtpi_lower_threshold=-0.58
2025-07-16 06:05:11,067 - [KRAKEN] - root - INFO - timeframe=1d, analysis_start_date='2025-02-10'
2025-07-16 06:05:11,067 - [KRAKEN] - root - INFO - n_assets=1, use_weighted_allocation=False
2025-07-16 06:05:11,067 - [KRAKEN] - root - INFO - Using provided trend method: PGO For Loop
2025-07-16 06:05:11,067 - [KRAKEN] - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-07-16 06:05:11,116 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:11,117 - [KRAKEN] - root - INFO - Saving configuration to config/settings_kraken_eur.yaml...
2025-07-16 06:05:11,132 - [KRAKEN] - root - INFO - Configuration saved successfully.
2025-07-16 06:05:11,132 - [KRAKEN] - root - INFO - Trend detection assets (USDT): ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-16 06:05:11,132 - [KRAKEN] - root - INFO - Number of trend detection assets: 14
2025-07-16 06:05:11,132 - [KRAKEN] - root - INFO - Selected assets type: <class 'list'>
2025-07-16 06:05:11,132 - [KRAKEN] - root - INFO - Trading execution assets (EUR): ['BTC/EUR', 'ETH/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AVAX/EUR', 'ADA/EUR', 'AAVE/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:05:11,132 - [KRAKEN] - root - INFO - Number of trading assets: 14
2025-07-16 06:05:11,133 - [KRAKEN] - root - INFO - Trading assets type: <class 'list'>
2025-07-16 06:05:11,538 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:05:11,567 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:11,602 - [KRAKEN] - root - INFO - Loading configuration from config/settings_kraken_eur.yaml...
2025-07-16 06:05:11,629 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:11,630 - [KRAKEN] - root - INFO - Execution context: backtesting
2025-07-16 06:05:11,630 - [KRAKEN] - root - INFO - Execution timing: candle_close
2025-07-16 06:05:11,630 - [KRAKEN] - root - INFO - Ratio calculation method: independent
2025-07-16 06:05:11,630 - [KRAKEN] - root - INFO - Tie-breaking strategy: incumbent
2025-07-16 06:05:11,630 - [KRAKEN] - root - INFO - Asset trend PGO parameters: length=None, upper_threshold=None, lower_threshold=None
2025-07-16 06:05:11,630 - [KRAKEN] - root - INFO - MTPI indicators override: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 06:05:11,630 - [KRAKEN] - root - INFO - MTPI combination method override: consensus
2025-07-16 06:05:11,630 - [KRAKEN] - root - INFO - MTPI long threshold override: 0.1
2025-07-16 06:05:11,630 - [KRAKEN] - root - INFO - MTPI short threshold override: -0.1
2025-07-16 06:05:11,630 - [KRAKEN] - root - INFO - Using standard warmup period of 60 days for equal allocation
2025-07-16 06:05:11,630 - [KRAKEN] - root - INFO - Fetching data from 2024-12-12 to ensure proper warmup (analysis starts at 2025-02-10)
2025-07-16 06:05:11,634 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:11,634 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:11,635 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:11,635 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:11,635 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:11,635 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:11,637 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:11,638 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:11,638 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:11,638 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:11,638 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:11,638 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:11,638 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:11,638 - [KRAKEN] - root - INFO - Loaded metadata for 48 assets
2025-07-16 06:05:11,638 - [KRAKEN] - root - INFO - Checking cache for 14 symbols (1d)...
2025-07-16 06:05:11,694 - [KRAKEN] - root - INFO - Loaded 216 rows of ETH/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:11,698 - [KRAKEN] - root - INFO - Metadata for ETH/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:11,699 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:11,699 - [KRAKEN] - root - INFO - Loaded 216 rows of ETH/USDT data from cache (after filtering).
2025-07-16 06:05:11,746 - [KRAKEN] - root - INFO - Loaded 216 rows of BTC/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:11,747 - [KRAKEN] - root - INFO - Metadata for BTC/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:11,748 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:11,748 - [KRAKEN] - root - INFO - Loaded 216 rows of BTC/USDT data from cache (after filtering).
2025-07-16 06:05:11,787 - [KRAKEN] - root - INFO - Loaded 216 rows of SOL/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:11,790 - [KRAKEN] - root - INFO - Metadata for SOL/USDT shows data starting from 2020-08-11, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:11,791 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:11,791 - [KRAKEN] - root - INFO - Loaded 216 rows of SOL/USDT data from cache (after filtering).
2025-07-16 06:05:11,817 - [KRAKEN] - root - INFO - Loaded 216 rows of SUI/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:11,821 - [KRAKEN] - root - INFO - Metadata for SUI/USDT shows data starting from 2023-05-03, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:11,823 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:11,823 - [KRAKEN] - root - INFO - Loaded 216 rows of SUI/USDT data from cache (after filtering).
2025-07-16 06:05:11,864 - [KRAKEN] - root - INFO - Loaded 216 rows of XRP/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:11,868 - [KRAKEN] - root - INFO - Metadata for XRP/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:11,868 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:11,869 - [KRAKEN] - root - INFO - Loaded 216 rows of XRP/USDT data from cache (after filtering).
2025-07-16 06:05:11,917 - [KRAKEN] - root - INFO - Loaded 216 rows of AAVE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:11,921 - [KRAKEN] - root - INFO - Metadata for AAVE/USDT shows data starting from 2020-10-15, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:11,922 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:11,922 - [KRAKEN] - root - INFO - Loaded 216 rows of AAVE/USDT data from cache (after filtering).
2025-07-16 06:05:11,969 - [KRAKEN] - root - INFO - Loaded 216 rows of AVAX/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:11,972 - [KRAKEN] - root - INFO - Metadata for AVAX/USDT shows data starting from 2020-09-22, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:11,973 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:11,973 - [KRAKEN] - root - INFO - Loaded 216 rows of AVAX/USDT data from cache (after filtering).
2025-07-16 06:05:12,022 - [KRAKEN] - root - INFO - Loaded 216 rows of ADA/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:12,025 - [KRAKEN] - root - INFO - Metadata for ADA/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:12,028 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:12,028 - [KRAKEN] - root - INFO - Loaded 216 rows of ADA/USDT data from cache (after filtering).
2025-07-16 06:05:12,074 - [KRAKEN] - root - INFO - Loaded 216 rows of LINK/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:12,078 - [KRAKEN] - root - INFO - Metadata for LINK/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:12,079 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:12,079 - [KRAKEN] - root - INFO - Loaded 216 rows of LINK/USDT data from cache (after filtering).
2025-07-16 06:05:12,133 - [KRAKEN] - root - INFO - Loaded 216 rows of TRX/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:12,135 - [KRAKEN] - root - INFO - Metadata for TRX/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:12,137 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:12,137 - [KRAKEN] - root - INFO - Loaded 216 rows of TRX/USDT data from cache (after filtering).
2025-07-16 06:05:12,164 - [KRAKEN] - root - INFO - Loaded 216 rows of PEPE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:12,167 - [KRAKEN] - root - INFO - Metadata for PEPE/USDT shows data starting from 2023-05-05, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:12,168 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:12,168 - [KRAKEN] - root - INFO - Loaded 216 rows of PEPE/USDT data from cache (after filtering).
2025-07-16 06:05:12,231 - [KRAKEN] - root - INFO - Loaded 216 rows of DOGE/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:12,235 - [KRAKEN] - root - INFO - Metadata for DOGE/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:12,236 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:12,236 - [KRAKEN] - root - INFO - Loaded 216 rows of DOGE/USDT data from cache (after filtering).
2025-07-16 06:05:12,305 - [KRAKEN] - root - INFO - Loaded 216 rows of BNB/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:12,310 - [KRAKEN] - root - INFO - Metadata for BNB/USDT shows data starting from 2019-08-20, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:12,313 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:12,313 - [KRAKEN] - root - INFO - Loaded 216 rows of BNB/USDT data from cache (after filtering).
2025-07-16 06:05:12,353 - [KRAKEN] - root - INFO - Loaded 216 rows of DOT/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:12,354 - [KRAKEN] - root - INFO - Metadata for DOT/USDT shows data starting from 2020-08-18, which is before or equal to the effective start date 2024-12-12. Using cached data.
2025-07-16 06:05:12,355 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:12,355 - [KRAKEN] - root - INFO - Loaded 216 rows of DOT/USDT data from cache (after filtering).
2025-07-16 06:05:12,355 - [KRAKEN] - root - INFO - All 14 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-16 06:05:12,357 - [KRAKEN] - root - INFO - Asset ETH/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:12,357 - [KRAKEN] - root - INFO - Asset BTC/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:12,358 - [KRAKEN] - root - INFO - Asset SOL/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:12,358 - [KRAKEN] - root - INFO - Asset SUI/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:12,358 - [KRAKEN] - root - INFO - Asset XRP/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:12,358 - [KRAKEN] - root - INFO - Asset AAVE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:12,358 - [KRAKEN] - root - INFO - Asset AVAX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:12,358 - [KRAKEN] - root - INFO - Asset ADA/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:12,358 - [KRAKEN] - root - INFO - Asset LINK/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:12,358 - [KRAKEN] - root - INFO - Asset TRX/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:12,358 - [KRAKEN] - root - INFO - Asset PEPE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:12,359 - [KRAKEN] - root - INFO - Asset DOGE/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:12,359 - [KRAKEN] - root - INFO - Asset BNB/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:12,359 - [KRAKEN] - root - INFO - Asset DOT/USDT first available date: 2024-12-12 00:00:00+00:00
2025-07-16 06:05:12,439 - [KRAKEN] - root - INFO - Using standard MTPI warmup period of 120 days
2025-07-16 06:05:12,441 - [KRAKEN] - root - INFO - Fetching MTPI signals from 2024-10-13 to ensure proper warmup (analysis starts at 2025-02-10)
2025-07-16 06:05:12,441 - [KRAKEN] - root - INFO - Fetching MTPI signals using trend method: PGO For Loop
2025-07-16 06:05:12,441 - [KRAKEN] - root - INFO - Using multi-indicator MTPI with config override: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-07-16 06:05:12,441 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:05:12,467 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:12,467 - [KRAKEN] - root - INFO - Loaded MTPI multi-indicator configuration with 8 enabled indicators
2025-07-16 06:05:12,467 - [KRAKEN] - root - INFO - Applying configuration overrides: {'enabled_indicators': ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], 'combination_method': 'consensus', 'long_threshold': 0.1, 'short_threshold': -0.1}
2025-07-16 06:05:12,467 - [KRAKEN] - root - INFO - Override: enabled_indicators = ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 06:05:12,467 - [KRAKEN] - root - INFO - Override: combination_method = consensus
2025-07-16 06:05:12,467 - [KRAKEN] - root - INFO - Override: long_threshold = 0.1
2025-07-16 06:05:12,467 - [KRAKEN] - root - INFO - Override: short_threshold = -0.1
2025-07-16 06:05:12,467 - [KRAKEN] - root - INFO - Using MTPI configuration: indicators=['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score'], method=consensus, thresholds=(-0.1, 0.1)
2025-07-16 06:05:12,468 - [KRAKEN] - root - INFO - Calculated appropriate limit for 1d timeframe: 500 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-16 06:05:12,468 - [KRAKEN] - root - INFO - Using adjusted limit: 500 for max indicator length: 60
2025-07-16 06:05:12,468 - [KRAKEN] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-16 06:05:12,515 - [KRAKEN] - root - INFO - Loaded 276 rows of BTC/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:05:12,516 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:05:12,516 - [KRAKEN] - root - INFO - Loaded 276 rows of BTC/USDT data from cache (after filtering).
2025-07-16 06:05:12,516 - [KRAKEN] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-16 06:05:12,516 - [KRAKEN] - root - INFO - Fetched BTC data: 276 candles from 2024-10-13 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:05:12,518 - [KRAKEN] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-16 06:05:12,628 - [KRAKEN] - root - INFO - Generated PGO Score signals: {-1: np.int64(111), 0: np.int64(34), 1: np.int64(131)}
2025-07-16 06:05:12,630 - [KRAKEN] - root - INFO - Generated pgo signals: 276 values
2025-07-16 06:05:12,631 - [KRAKEN] - root - INFO - Generating Bollinger Band signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False, heikin_src=close
2025-07-16 06:05:12,631 - [KRAKEN] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-16 06:05:12,671 - [KRAKEN] - root - INFO - Generated BB Score signals: {-1: np.int64(109), 0: np.int64(32), 1: np.int64(135)}
2025-07-16 06:05:12,674 - [KRAKEN] - root - INFO - Generated Bollinger Band signals: 276 values
2025-07-16 06:05:12,674 - [KRAKEN] - root - INFO - Generated bollinger_bands signals: 276 values
2025-07-16 06:05:14,010 - [KRAKEN] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-16 06:05:14,012 - [KRAKEN] - root - INFO - Generated dwma_score signals: 276 values
2025-07-16 06:05:14,166 - [KRAKEN] - root - INFO - Generated DEMA Supertrend signals
2025-07-16 06:05:14,167 - [KRAKEN] - root - INFO - Signal distribution: {-1: np.int64(156), 0: np.int64(1), 1: np.int64(119)}
2025-07-16 06:05:14,167 - [KRAKEN] - root - INFO - Generated DEMA Super Score signals
2025-07-16 06:05:14,167 - [KRAKEN] - root - INFO - Generated dema_super_score signals: 276 values
2025-07-16 06:05:14,564 - [KRAKEN] - root - INFO - Generated DPSD signals
2025-07-16 06:05:14,566 - [KRAKEN] - root - INFO - Signal distribution: {-1: np.int64(105), 0: np.int64(87), 1: np.int64(84)}
2025-07-16 06:05:14,567 - [KRAKEN] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-16 06:05:14,567 - [KRAKEN] - root - INFO - Generated dpsd_score signals: 276 values
2025-07-16 06:05:14,599 - [KRAKEN] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-16 06:05:14,600 - [KRAKEN] - root - INFO - Generated AAD Score signals using SMA method
2025-07-16 06:05:14,600 - [KRAKEN] - root - INFO - Generated aad_score signals: 276 values
2025-07-16 06:05:14,816 - [KRAKEN] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-16 06:05:14,818 - [KRAKEN] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-16 06:05:14,818 - [KRAKEN] - root - INFO - Generated dynamic_ema_score signals: 276 values
2025-07-16 06:05:15,239 - [KRAKEN] - root - INFO - Generated quantile_dema_score signals: 276 values
2025-07-16 06:05:15,267 - [KRAKEN] - root - INFO - Combined 8 signals using arithmetic mean aggregation
2025-07-16 06:05:15,268 - [KRAKEN] - root - INFO - Signal distribution: {1: 151, -1: 124, 0: 1}
2025-07-16 06:05:15,268 - [KRAKEN] - root - INFO - Generated combined MTPI signals: 276 values using consensus method
2025-07-16 06:05:15,270 - [KRAKEN] - root - INFO - Signal distribution: {1: 151, -1: 124, 0: 1}
2025-07-16 06:05:15,271 - [KRAKEN] - root - INFO - Calculating daily scores using trend method: PGO For Loop...
2025-07-16 06:05:15,276 - [KRAKEN] - root - INFO - Saving configuration to config/settings.yaml...
2025-07-16 06:05:15,290 - [KRAKEN] - root - INFO - Configuration saved successfully.
2025-07-16 06:05:15,291 - [KRAKEN] - root - INFO - Updated config with trend method: PGO For Loop and PGO parameters
2025-07-16 06:05:15,291 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:05:15,315 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:15,315 - [KRAKEN] - root - INFO - Using ratio-based approach with PGO (PGO For Loop)...
2025-07-16 06:05:15,315 - [KRAKEN] - root - INFO - Calculating ratio PGO signals for 14 assets with PGO(35) using full OHLCV data
2025-07-16 06:05:15,315 - [KRAKEN] - root - INFO - Using ratio calculation method: independent
2025-07-16 06:05:15,387 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:15,470 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:15,540 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:15,544 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:15,621 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:15,643 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:15,726 - [KRAKEN] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-16 06:05:15,726 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:15,809 - [KRAKEN] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-16 06:05:15,836 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:15,904 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:15,904 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:15,968 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:15,988 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:16,107 - [KRAKEN] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:16,107 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:16,176 - [KRAKEN] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:16,196 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:16,272 - [KRAKEN] - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-07-16 06:05:16,274 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:16,351 - [KRAKEN] - root - WARNING - Found 29 extreme PGO values (abs > 10)
2025-07-16 06:05:16,374 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:16,445 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:16,446 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:16,511 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:16,532 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:16,615 - [KRAKEN] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:05:16,616 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:16,680 - [KRAKEN] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:05:16,704 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:16,769 - [KRAKEN] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:05:16,771 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:16,836 - [KRAKEN] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:05:16,856 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:16,929 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:16,929 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:16,995 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:17,013 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:17,092 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:17,092 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:17,156 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:17,175 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:17,250 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:17,350 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:17,434 - [KRAKEN] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-16 06:05:17,436 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:17,501 - [KRAKEN] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-16 06:05:17,525 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ETH/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:17,617 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:17,617 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:17,684 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:17,704 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:17,777 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:17,867 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:17,938 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:17,938 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,004 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:18,023 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:18,103 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:18,103 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,171 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:18,194 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:18,270 - [KRAKEN] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:18,270 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,334 - [KRAKEN] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:18,355 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:18,435 - [KRAKEN] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-16 06:05:18,437 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,497 - [KRAKEN] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-16 06:05:18,516 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:18,587 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:18,588 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,648 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:18,668 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:18,744 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:18,746 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,823 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:18,840 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:18,905 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:18,907 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:18,968 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:18,987 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:19,099 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:19,101 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:19,171 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:19,195 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:19,270 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:19,357 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:19,439 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:19,547 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:19,629 - [KRAKEN] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-16 06:05:19,630 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:19,690 - [KRAKEN] - root - WARNING - Found 14 extreme PGO values (abs > 10)
2025-07-16 06:05:19,714 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BTC/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:19,794 - [KRAKEN] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-16 06:05:19,794 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:19,862 - [KRAKEN] - root - WARNING - Found 7 extreme PGO values (abs > 10)
2025-07-16 06:05:19,883 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:19,962 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:20,062 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:20,138 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:20,138 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:20,199 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:20,218 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:20,290 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:20,290 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:20,370 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:20,402 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:20,526 - [KRAKEN] - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-16 06:05:20,528 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:20,626 - [KRAKEN] - root - WARNING - Found 21 extreme PGO values (abs > 10)
2025-07-16 06:05:20,656 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:20,780 - [KRAKEN] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:05:20,780 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:20,884 - [KRAKEN] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:05:20,913 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:21,031 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:21,032 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:21,096 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:21,115 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:21,184 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:21,186 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:21,244 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:21,263 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:21,345 - [KRAKEN] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-16 06:05:21,347 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:21,405 - [KRAKEN] - root - WARNING - Found 12 extreme PGO values (abs > 10)
2025-07-16 06:05:21,423 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:21,493 - [KRAKEN] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:05:21,495 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:21,579 - [KRAKEN] - root - WARNING - Found 25 extreme PGO values (abs > 10)
2025-07-16 06:05:21,627 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:21,719 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:21,721 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:21,782 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:21,800 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:21,867 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:21,868 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:21,927 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:21,947 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:22,014 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:22,015 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:22,072 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:22,094 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SOL/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:22,162 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:22,162 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:22,222 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:22,243 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:22,312 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:22,392 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:22,478 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:22,574 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:22,652 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:22,734 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:22,815 - [KRAKEN] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-16 06:05:22,817 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:22,883 - [KRAKEN] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-16 06:05:22,907 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:22,991 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:23,089 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:23,173 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:23,271 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:23,351 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:23,352 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:23,440 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:23,473 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:23,548 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:23,643 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:23,734 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:23,839 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:23,933 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:23,935 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:24,002 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:24,024 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:24,102 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:24,196 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:24,283 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:24,378 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for SUI/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:24,483 - [KRAKEN] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-16 06:05:24,485 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:24,555 - [KRAKEN] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-16 06:05:24,582 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:24,654 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:24,743 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:24,827 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:24,910 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:24,987 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:24,989 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:25,087 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:25,106 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:25,182 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:25,186 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:25,248 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:25,269 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:25,343 - [KRAKEN] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-16 06:05:25,343 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:25,407 - [KRAKEN] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-16 06:05:25,428 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:25,501 - [KRAKEN] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:25,503 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:25,573 - [KRAKEN] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:25,592 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:25,673 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:25,675 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:25,747 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:25,773 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:25,849 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:25,851 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:25,923 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:25,944 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:26,016 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:26,016 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:26,084 - [KRAKEN] - root - WARNING - Found 20 extreme PGO values (abs > 10)
2025-07-16 06:05:26,104 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:26,182 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:26,288 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:26,377 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:26,490 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:26,560 - [KRAKEN] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:26,562 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:26,625 - [KRAKEN] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:26,657 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for XRP/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:26,780 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:26,910 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:26,985 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:27,073 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:27,141 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:27,229 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:27,325 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:27,417 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:27,495 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:27,591 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:27,671 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:27,673 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:27,739 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:27,761 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:27,914 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:27,916 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:28,017 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:28,040 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:28,152 - [KRAKEN] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:28,153 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:28,235 - [KRAKEN] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:28,256 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:28,333 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:28,430 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:28,507 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:28,594 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:28,665 - [KRAKEN] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:28,667 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:28,749 - [KRAKEN] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:28,772 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:28,853 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:28,951 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:29,039 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:29,041 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:29,142 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:29,175 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AAVE/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:29,318 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:29,453 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:29,541 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:29,634 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:29,718 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:29,805 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:29,953 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:30,044 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:30,125 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:30,218 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:30,303 - [KRAKEN] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-16 06:05:30,304 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:30,386 - [KRAKEN] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-16 06:05:30,412 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:30,499 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:30,599 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:30,667 - [KRAKEN] - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-07-16 06:05:30,669 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:30,733 - [KRAKEN] - root - WARNING - Found 36 extreme PGO values (abs > 10)
2025-07-16 06:05:30,751 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:30,821 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:30,909 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:30,987 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:31,068 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:31,136 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:31,214 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:31,279 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:31,358 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:31,437 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:31,514 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for AVAX/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:31,579 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:31,584 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:31,647 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:31,667 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:31,739 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:31,828 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:31,895 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:31,990 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:32,066 - [KRAKEN] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:32,067 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:32,126 - [KRAKEN] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:32,147 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:32,228 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:32,318 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:32,394 - [KRAKEN] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:05:32,394 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:32,454 - [KRAKEN] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:05:32,481 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:32,580 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:32,580 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:32,641 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:32,666 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:32,742 - [KRAKEN] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-16 06:05:32,744 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:32,804 - [KRAKEN] - root - WARNING - Found 22 extreme PGO values (abs > 10)
2025-07-16 06:05:32,826 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:32,905 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:32,988 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:33,063 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:33,065 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:33,127 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:33,147 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:33,218 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:33,220 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:33,290 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:33,313 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:33,379 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:33,462 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:33,550 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:33,550 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:33,610 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:33,631 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for ADA/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:33,703 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:33,791 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:33,870 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:34,008 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:34,104 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:34,185 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:34,285 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:34,389 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:34,458 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:34,541 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:34,619 - [KRAKEN] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-16 06:05:34,620 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:34,681 - [KRAKEN] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-16 06:05:34,703 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:34,777 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:34,861 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:34,932 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:35,046 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:35,121 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:35,200 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:35,269 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:35,271 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:35,333 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:35,353 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:35,421 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:35,524 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:35,602 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:35,687 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:35,758 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:35,844 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for LINK/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:35,914 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:35,992 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:36,063 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:36,143 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:36,211 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:36,211 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:36,272 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:36,291 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:36,365 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:36,455 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:36,531 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:36,532 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:36,603 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:36,623 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:36,719 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:36,719 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:36,780 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:36,802 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:36,895 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:36,896 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:36,975 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:36,999 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:37,071 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:37,159 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:37,259 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:37,259 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:37,341 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:37,360 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:37,425 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:37,425 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:37,504 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:37,536 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:37,624 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:37,704 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:37,776 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:37,856 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:37,928 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:38,007 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for TRX/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:38,078 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:38,166 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:38,236 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:38,319 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:38,395 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:38,489 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:38,596 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:38,672 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:38,741 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:38,818 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:38,892 - [KRAKEN] - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-07-16 06:05:38,894 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:38,958 - [KRAKEN] - root - WARNING - Found 32 extreme PGO values (abs > 10)
2025-07-16 06:05:38,978 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:39,051 - [KRAKEN] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:39,052 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:39,125 - [KRAKEN] - root - WARNING - Found 11 extreme PGO values (abs > 10)
2025-07-16 06:05:39,144 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:39,221 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:39,221 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:39,279 - [KRAKEN] - root - WARNING - Found 1 extreme PGO values (abs > 10)
2025-07-16 06:05:39,299 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:39,368 - [KRAKEN] - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-07-16 06:05:39,370 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:39,427 - [KRAKEN] - root - WARNING - Found 33 extreme PGO values (abs > 10)
2025-07-16 06:05:39,447 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:39,516 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:39,596 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:39,667 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:39,746 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:39,817 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:39,897 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:39,966 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:40,049 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for PEPE/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:40,131 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:40,211 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:40,313 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:40,408 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:40,480 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:40,576 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:40,647 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:40,648 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:40,725 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:40,750 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:40,842 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:40,842 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:40,915 - [KRAKEN] - root - WARNING - Found 3 extreme PGO values (abs > 10)
2025-07-16 06:05:40,935 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:41,012 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:41,012 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:41,077 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:41,097 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:41,172 - [KRAKEN] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-16 06:05:41,172 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:41,243 - [KRAKEN] - root - WARNING - Found 24 extreme PGO values (abs > 10)
2025-07-16 06:05:41,263 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:41,366 - [KRAKEN] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-16 06:05:41,366 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:41,477 - [KRAKEN] - root - WARNING - Found 5 extreme PGO values (abs > 10)
2025-07-16 06:05:41,509 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:41,602 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:41,602 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:41,676 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:41,696 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:41,783 - [KRAKEN] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:41,783 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:41,859 - [KRAKEN] - root - WARNING - Found 4 extreme PGO values (abs > 10)
2025-07-16 06:05:41,877 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:41,951 - [KRAKEN] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-16 06:05:41,952 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:42,019 - [KRAKEN] - root - WARNING - Found 17 extreme PGO values (abs > 10)
2025-07-16 06:05:42,040 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:42,142 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:42,230 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:42,303 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:42,303 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:42,367 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:42,387 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOGE/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:42,455 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:42,541 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:42,615 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:42,721 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:42,813 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:42,813 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:42,881 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:42,906 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:42,976 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:42,978 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:43,035 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:43,056 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:43,136 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:43,137 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:43,200 - [KRAKEN] - root - WARNING - Found 13 extreme PGO values (abs > 10)
2025-07-16 06:05:43,221 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:43,288 - [KRAKEN] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:05:43,290 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:43,349 - [KRAKEN] - root - WARNING - Found 19 extreme PGO values (abs > 10)
2025-07-16 06:05:43,368 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:43,439 - [KRAKEN] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-16 06:05:43,440 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:43,504 - [KRAKEN] - root - WARNING - Found 23 extreme PGO values (abs > 10)
2025-07-16 06:05:43,524 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:43,599 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:43,599 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:43,668 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:43,708 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:43,802 - [KRAKEN] - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-07-16 06:05:43,802 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:43,867 - [KRAKEN] - root - WARNING - Found 28 extreme PGO values (abs > 10)
2025-07-16 06:05:43,889 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:43,969 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:43,969 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:44,044 - [KRAKEN] - root - WARNING - Found 18 extreme PGO values (abs > 10)
2025-07-16 06:05:44,067 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:44,156 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:44,156 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:44,224 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:44,244 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:44,328 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:44,420 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:44,506 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:44,594 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for BNB/USDT/DOT/USDT using full OHLCV data
2025-07-16 06:05:44,685 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:44,778 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ETH/USDT using full OHLCV data
2025-07-16 06:05:44,858 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:44,938 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BTC/USDT using full OHLCV data
2025-07-16 06:05:45,007 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:45,104 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SOL/USDT using full OHLCV data
2025-07-16 06:05:45,175 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:45,175 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:45,234 - [KRAKEN] - root - WARNING - Found 8 extreme PGO values (abs > 10)
2025-07-16 06:05:45,253 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/SUI/USDT using full OHLCV data
2025-07-16 06:05:45,325 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:45,325 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:45,391 - [KRAKEN] - root - WARNING - Found 15 extreme PGO values (abs > 10)
2025-07-16 06:05:45,411 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/XRP/USDT using full OHLCV data
2025-07-16 06:05:45,487 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:45,487 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:45,556 - [KRAKEN] - root - WARNING - Found 9 extreme PGO values (abs > 10)
2025-07-16 06:05:45,576 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AAVE/USDT using full OHLCV data
2025-07-16 06:05:45,648 - [KRAKEN] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-16 06:05:45,648 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:45,711 - [KRAKEN] - root - WARNING - Found 26 extreme PGO values (abs > 10)
2025-07-16 06:05:45,734 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/AVAX/USDT using full OHLCV data
2025-07-16 06:05:45,803 - [KRAKEN] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-16 06:05:45,805 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:45,870 - [KRAKEN] - root - WARNING - Found 10 extreme PGO values (abs > 10)
2025-07-16 06:05:45,890 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/ADA/USDT using full OHLCV data
2025-07-16 06:05:45,962 - [KRAKEN] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:45,963 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:46,026 - [KRAKEN] - root - WARNING - Found 16 extreme PGO values (abs > 10)
2025-07-16 06:05:46,049 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/LINK/USDT using full OHLCV data
2025-07-16 06:05:46,122 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:46,211 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/TRX/USDT using full OHLCV data
2025-07-16 06:05:46,294 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:46,294 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:46,371 - [KRAKEN] - root - WARNING - Found 2 extreme PGO values (abs > 10)
2025-07-16 06:05:46,397 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/PEPE/USDT using full OHLCV data
2025-07-16 06:05:46,472 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:46,552 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/DOGE/USDT using full OHLCV data
2025-07-16 06:05:46,622 - [KRAKEN] - root - INFO - Generating PGO signals with length=35, upper_threshold=1.1, lower_threshold=-0.58, skip_warmup=False
2025-07-16 06:05:46,715 - [KRAKEN] - root - INFO - Calculated independent ratio PGO signal for DOT/USDT/BNB/USDT using full OHLCV data
2025-07-16 06:05:55,712 - [KRAKEN] - root - INFO - Successfully calculated daily scores using ratio-based comparisons.
2025-07-16 06:05:55,714 - [KRAKEN] - root - INFO - Latest MTPI signal is 1
2025-07-16 06:05:55,715 - [KRAKEN] - root - INFO - Latest MTPI signal is bullish (1), will proceed with normal asset selection
2025-07-16 06:05:55,715 - [KRAKEN] - root - INFO - Finished calculating daily scores. DataFrame shape: (216, 14)
2025-07-16 06:05:55,715 - [KRAKEN] - root - WARNING - Running strategy with n_assets=1, equal allocation, MTPI filtering ENABLED
2025-07-16 06:05:55,736 - [KRAKEN] - root - INFO - Date ranges for each asset:
2025-07-16 06:05:55,738 - [KRAKEN] - root - INFO -   ETH/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:55,739 - [KRAKEN] - root - INFO -   BTC/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:55,739 - [KRAKEN] - root - INFO -   SOL/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:55,739 - [KRAKEN] - root - INFO -   SUI/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:55,739 - [KRAKEN] - root - INFO -   XRP/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:55,739 - [KRAKEN] - root - INFO -   AAVE/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:55,739 - [KRAKEN] - root - INFO -   AVAX/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:55,739 - [KRAKEN] - root - INFO -   ADA/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:55,740 - [KRAKEN] - root - INFO -   LINK/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:55,740 - [KRAKEN] - root - INFO -   TRX/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:55,740 - [KRAKEN] - root - INFO -   PEPE/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:55,740 - [KRAKEN] - root - INFO -   DOGE/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:55,740 - [KRAKEN] - root - INFO -   BNB/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:55,742 - [KRAKEN] - root - INFO -   DOT/USDT: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:55,742 - [KRAKEN] - root - INFO - Common dates range: 2024-12-12 to 2025-07-15 (216 candles)
2025-07-16 06:05:55,743 - [KRAKEN] - root - INFO - Analysis will run from: 2025-02-10 to 2025-07-15 (156 candles)
2025-07-16 06:05:55,756 - [KRAKEN] - root - INFO - EXECUTION TIMING VERIFICATION:
2025-07-16 06:05:55,756 - [KRAKEN] - root - INFO -    Execution Method: candle_close
2025-07-16 06:05:55,756 - [KRAKEN] - root - INFO -    Automatic execution at 00:00 UTC (candle close)
2025-07-16 06:05:55,758 - [KRAKEN] - root - INFO -    Signal generated and executed immediately
2025-07-16 06:05:55,789 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-10 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 10.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 8.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:55,790 - [KRAKEN] - root - INFO - Including ETH/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,790 - [KRAKEN] - root - INFO - Including BTC/USDT on 2025-02-10 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,790 - [KRAKEN] - root - INFO - Including SOL/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,790 - [KRAKEN] - root - INFO - Including SUI/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,790 - [KRAKEN] - root - INFO - Including XRP/USDT on 2025-02-10 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,790 - [KRAKEN] - root - INFO - Including AAVE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,790 - [KRAKEN] - root - INFO - Including AVAX/USDT on 2025-02-10 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,792 - [KRAKEN] - root - INFO - Including ADA/USDT on 2025-02-10 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,792 - [KRAKEN] - root - INFO - Including LINK/USDT on 2025-02-10 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,793 - [KRAKEN] - root - INFO - Including TRX/USDT on 2025-02-10 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,793 - [KRAKEN] - root - INFO - Including PEPE/USDT on 2025-02-10 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,793 - [KRAKEN] - root - INFO - Including DOGE/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,793 - [KRAKEN] - root - INFO - Including BNB/USDT on 2025-02-10 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,793 - [KRAKEN] - root - INFO - Including DOT/USDT on 2025-02-10 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,793 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,793 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,793 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,793 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,797 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-11 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 9.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:55,797 - [KRAKEN] - root - INFO - Including ETH/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,797 - [KRAKEN] - root - INFO - Including BTC/USDT on 2025-02-11 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,797 - [KRAKEN] - root - INFO - Including SOL/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,797 - [KRAKEN] - root - INFO - Including SUI/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,797 - [KRAKEN] - root - INFO - Including XRP/USDT on 2025-02-11 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,797 - [KRAKEN] - root - INFO - Including AAVE/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,797 - [KRAKEN] - root - INFO - Including AVAX/USDT on 2025-02-11 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,797 - [KRAKEN] - root - INFO - Including ADA/USDT on 2025-02-11 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,797 - [KRAKEN] - root - INFO - Including LINK/USDT on 2025-02-11 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,797 - [KRAKEN] - root - INFO - Including TRX/USDT on 2025-02-11 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,798 - [KRAKEN] - root - INFO - Including PEPE/USDT on 2025-02-11 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,798 - [KRAKEN] - root - INFO - Including DOGE/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,798 - [KRAKEN] - root - INFO - Including BNB/USDT on 2025-02-11 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,798 - [KRAKEN] - root - INFO - Including DOT/USDT on 2025-02-11 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,798 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,798 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,798 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,798 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,801 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-12 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 9.0, 'SUI/USDT': 4.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:55,802 - [KRAKEN] - root - INFO - Including ETH/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,802 - [KRAKEN] - root - INFO - Including BTC/USDT on 2025-02-12 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,802 - [KRAKEN] - root - INFO - Including SOL/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,802 - [KRAKEN] - root - INFO - Including SUI/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,802 - [KRAKEN] - root - INFO - Including XRP/USDT on 2025-02-12 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,802 - [KRAKEN] - root - INFO - Including AAVE/USDT on 2025-02-12 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,804 - [KRAKEN] - root - INFO - Including AVAX/USDT on 2025-02-12 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,804 - [KRAKEN] - root - INFO - Including ADA/USDT on 2025-02-12 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,805 - [KRAKEN] - root - INFO - Including LINK/USDT on 2025-02-12 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,805 - [KRAKEN] - root - INFO - Including TRX/USDT on 2025-02-12 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,805 - [KRAKEN] - root - INFO - Including PEPE/USDT on 2025-02-12 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,805 - [KRAKEN] - root - INFO - Including DOGE/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,805 - [KRAKEN] - root - INFO - Including BNB/USDT on 2025-02-12 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,805 - [KRAKEN] - root - INFO - Including DOT/USDT on 2025-02-12 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,805 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,805 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,805 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,805 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,809 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-13 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:55,809 - [KRAKEN] - root - INFO - Including ETH/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,809 - [KRAKEN] - root - INFO - Including BTC/USDT on 2025-02-13 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,809 - [KRAKEN] - root - INFO - Including SOL/USDT on 2025-02-13 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,810 - [KRAKEN] - root - INFO - Including SUI/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,810 - [KRAKEN] - root - INFO - Including XRP/USDT on 2025-02-13 00:00:00+00:00 with score 9.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,810 - [KRAKEN] - root - INFO - Including AAVE/USDT on 2025-02-13 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,810 - [KRAKEN] - root - INFO - Including AVAX/USDT on 2025-02-13 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,810 - [KRAKEN] - root - INFO - Including ADA/USDT on 2025-02-13 00:00:00+00:00 with score 7.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,810 - [KRAKEN] - root - INFO - Including LINK/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,810 - [KRAKEN] - root - INFO - Including TRX/USDT on 2025-02-13 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,810 - [KRAKEN] - root - INFO - Including PEPE/USDT on 2025-02-13 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,810 - [KRAKEN] - root - INFO - Including DOGE/USDT on 2025-02-13 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,812 - [KRAKEN] - root - INFO - Including BNB/USDT on 2025-02-13 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,812 - [KRAKEN] - root - INFO - Including DOT/USDT on 2025-02-13 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,813 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,813 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,813 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,813 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,816 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-14 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:55,816 - [KRAKEN] - root - INFO - Including ETH/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,816 - [KRAKEN] - root - INFO - Including BTC/USDT on 2025-02-14 00:00:00+00:00 with score 11.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,817 - [KRAKEN] - root - INFO - Including SOL/USDT on 2025-02-14 00:00:00+00:00 with score 8.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,817 - [KRAKEN] - root - INFO - Including SUI/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,817 - [KRAKEN] - root - INFO - Including XRP/USDT on 2025-02-14 00:00:00+00:00 with score 10.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,817 - [KRAKEN] - root - INFO - Including AAVE/USDT on 2025-02-14 00:00:00+00:00 with score 5.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,817 - [KRAKEN] - root - INFO - Including AVAX/USDT on 2025-02-14 00:00:00+00:00 with score 2.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,817 - [KRAKEN] - root - INFO - Including ADA/USDT on 2025-02-14 00:00:00+00:00 with score 6.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,817 - [KRAKEN] - root - INFO - Including LINK/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,817 - [KRAKEN] - root - INFO - Including TRX/USDT on 2025-02-14 00:00:00+00:00 with score 12.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,817 - [KRAKEN] - root - INFO - Including PEPE/USDT on 2025-02-14 00:00:00+00:00 with score 1.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,817 - [KRAKEN] - root - INFO - Including DOGE/USDT on 2025-02-14 00:00:00+00:00 with score 4.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,817 - [KRAKEN] - root - INFO - Including BNB/USDT on 2025-02-14 00:00:00+00:00 with score 13.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,817 - [KRAKEN] - root - INFO - Including DOT/USDT on 2025-02-14 00:00:00+00:00 with score 3.0 (first date: 2024-12-12 00:00:00+00:00)
2025-07-16 06:05:55,817 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,818 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,818 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,818 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,821 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-15 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:55,821 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,821 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,822 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,822 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,825 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-16 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 6.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 5.0}
2025-07-16 06:05:55,825 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,825 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,825 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,825 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,829 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-17 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 13.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:55,829 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,829 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,829 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,829 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,833 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-18 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 7.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:55,833 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,833 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,833 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,833 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,836 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-19 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 11.0, 'SOL/USDT': 2.0, 'SUI/USDT': 5.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 8.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:55,837 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,837 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,837 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,837 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,840 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-20 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 8.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 8.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 5.0}
2025-07-16 06:05:55,840 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,840 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,841 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,841 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,842 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-21 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:55,844 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,844 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,844 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,844 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,845 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-22 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 6.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 13.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:55,846 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,846 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,846 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,846 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,849 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-23 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 5.0}
2025-07-16 06:05:55,849 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,849 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,849 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,850 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,852 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-24 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:55,853 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,853 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,853 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,853 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,854 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-25 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 7.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 2.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:55,854 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,854 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,858 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,858 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,860 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-26 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:55,860 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,861 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,861 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,861 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,863 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-27 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 5.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 5.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 12.0, 'DOT/USDT': 10.0}
2025-07-16 06:05:55,863 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,863 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,863 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,863 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,867 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-02-28 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 6.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 5.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 3.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 12.0, 'DOT/USDT': 11.0}
2025-07-16 06:05:55,867 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,868 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,868 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,868 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,871 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-01 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 10.0, 'SOL/USDT': 0.0, 'SUI/USDT': 6.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 2.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 2.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 11.0}
2025-07-16 06:05:55,871 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,871 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,871 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,871 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,875 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-02 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 1.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 9.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:55,875 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,875 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,875 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,875 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,878 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-03 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 2.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 9.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:55,879 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,879 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,879 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,879 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,880 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-04 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 1.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 13.0, 'LINK/USDT': 2.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:55,880 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,880 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,882 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,882 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,884 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-05 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 1.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 7.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:55,884 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,884 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,886 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,886 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,887 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-06 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:55,888 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,888 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,888 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,888 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,891 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-07 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 13.0, 'LINK/USDT': 8.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:55,891 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,892 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,892 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,892 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,895 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-08 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 3.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 13.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:55,895 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,895 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,895 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,895 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,898 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-09 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 3.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:55,899 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,899 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,899 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,899 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,900 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-10 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:55,902 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,902 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,902 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,903 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,904 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-11 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 12.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:55,904 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,904 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,906 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,906 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,907 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-12 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 1.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 3.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:55,908 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,908 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,908 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,908 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,911 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-13 00:00:00+00:00: {'ETH/USDT': 4.0, 'BTC/USDT': 9.0, 'SOL/USDT': 3.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 4.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:55,911 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,911 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,911 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,911 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,915 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-14 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 6.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:55,915 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,915 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,915 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,915 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,918 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-15 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 12.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 6.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 11.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:55,919 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,919 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,919 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,919 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,920 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-16 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 10.0, 'LINK/USDT': 5.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 12.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:55,920 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,922 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,922 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,922 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,924 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-17 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 9.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 10.0, 'LINK/USDT': 5.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:55,924 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,926 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,926 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,926 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,928 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-18 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 9.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:55,928 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,928 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,928 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,928 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,931 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-19 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 8.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 9.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:55,932 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,932 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,932 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,932 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,934 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-20 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 12.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:55,937 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,937 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,938 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,938 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,939 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-21 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:55,939 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,939 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,940 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,940 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,943 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-22 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 8.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:55,944 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,944 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,944 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,944 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,947 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-23 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 6.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 13.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:55,948 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,948 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,948 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,948 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,951 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-24 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 7.0, 'SOL/USDT': 4.0, 'SUI/USDT': 0.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 2.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 13.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:55,951 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,951 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,952 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,952 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,955 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-25 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 5.0, 'SOL/USDT': 4.0, 'SUI/USDT': 0.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 1.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:55,955 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,955 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,955 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,955 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,959 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-26 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 2.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 10.0, 'DOT/USDT': 9.0}
2025-07-16 06:05:55,959 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,959 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,959 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,959 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,962 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-27 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 9.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:55,962 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,962 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,962 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,962 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,964 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-28 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 11.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:55,964 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,964 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,964 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,964 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,967 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-29 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:55,967 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,967 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,968 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,968 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,971 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-30 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 2.0, 'SUI/USDT': 8.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:55,972 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,972 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,972 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,972 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,976 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-03-31 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 7.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:55,978 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,978 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,978 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,979 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,982 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-01 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 7.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:55,982 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,983 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,983 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,983 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,984 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-02 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:55,986 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,986 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,986 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,987 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,988 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-03 00:00:00+00:00: {'ETH/USDT': 2.0, 'BTC/USDT': 10.0, 'SOL/USDT': 1.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 12.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:55,988 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,988 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,988 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,990 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,991 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-04 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 8.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:55,992 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,992 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,992 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,992 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,995 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-05 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 8.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 12.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:55,996 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,996 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,996 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,996 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:55,999 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-06 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 3.0, 'SUI/USDT': 5.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 12.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:55,999 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:55,999 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:55,999 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:55,999 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,003 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-07 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 5.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 11.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:56,003 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,003 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,003 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,003 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,009 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-08 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 5.0, 'SUI/USDT': 5.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 1.0, 'LINK/USDT': 3.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 5.0}
2025-07-16 06:05:56,009 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,009 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,009 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,010 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,013 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-09 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 1.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,013 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,013 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,013 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,014 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,017 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-10 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,017 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,018 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,018 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,018 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,022 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-11 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 11.0, 'SOL/USDT': 6.0, 'SUI/USDT': 6.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,022 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,024 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,024 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,024 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,028 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-12 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,028 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,029 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,029 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,029 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,032 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-13 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 5.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,032 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,033 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,033 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,033 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,034 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-14 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 7.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,036 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,036 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,036 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,036 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,038 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-15 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 8.0, 'SUI/USDT': 5.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 10.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,038 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,038 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,038 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,040 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,042 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-16 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,042 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,042 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,042 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,042 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,045 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-17 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 12.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,046 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,046 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,046 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,046 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,049 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-18 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 12.0, 'SUI/USDT': 4.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,049 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,049 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,050 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,050 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,053 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-19 00:00:00+00:00: {'ETH/USDT': 1.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 4.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 5.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,054 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,054 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,054 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,054 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,056 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-20 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 3.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 5.0}
2025-07-16 06:05:56,057 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,057 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,059 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,059 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,061 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-21 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 10.0, 'SOL/USDT': 13.0, 'SUI/USDT': 3.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:56,061 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,063 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,063 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,063 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,065 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-22 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 9.0, 'SOL/USDT': 12.0, 'SUI/USDT': 9.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 0.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 2.0, 'LINK/USDT': 5.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 5.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,065 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,065 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,067 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,067 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,067 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-04-23:
2025-07-16 06:05:56,067 - [KRAKEN] - root - INFO -    Signal Date: 2025-04-22 (generated at 00:00 UTC)
2025-07-16 06:05:56,068 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-23 00:00 UTC (immediate)
2025-07-16 06:05:56,068 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:56,068 - [KRAKEN] - root - INFO -    Buying: ['SOL/USDT']
2025-07-16 06:05:56,068 - [KRAKEN] - root - INFO -    SOL/USDT buy price: $151.1000 (close price)
2025-07-16 06:05:56,071 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-23 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,071 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,071 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,072 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,072 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,072 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-04-24:
2025-07-16 06:05:56,072 - [KRAKEN] - root - INFO -    Signal Date: 2025-04-23 (generated at 00:00 UTC)
2025-07-16 06:05:56,072 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-04-24 00:00 UTC (immediate)
2025-07-16 06:05:56,072 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:56,072 - [KRAKEN] - root - INFO -    Selling: ['SOL/USDT']
2025-07-16 06:05:56,072 - [KRAKEN] - root - INFO -    Buying: ['SUI/USDT']
2025-07-16 06:05:56,072 - [KRAKEN] - root - INFO -    SUI/USDT buy price: $3.3437 (close price)
2025-07-16 06:05:56,076 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-24 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 3.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:56,076 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,076 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,076 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,076 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,079 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-25 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 8.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 4.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:56,080 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,080 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,080 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,080 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,083 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-26 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:56,084 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,084 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,084 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,084 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,087 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-27 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 9.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:56,088 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,088 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,088 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,088 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,089 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-28 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:56,091 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,091 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,091 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,092 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,095 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-29 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 8.0, 'ADA/USDT': 5.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:56,095 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,095 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,096 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,096 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,099 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-04-30 00:00:00+00:00: {'ETH/USDT': 0.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:56,099 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,100 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,100 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,100 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,101 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-01 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 7.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 7.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:56,103 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,103 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,103 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,103 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,105 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-02 00:00:00+00:00: {'ETH/USDT': 3.0, 'BTC/USDT': 8.0, 'SOL/USDT': 10.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:56,107 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,107 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,108 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,108 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,111 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-03 00:00:00+00:00: {'ETH/USDT': 5.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 6.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,112 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,112 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,112 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,112 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,115 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-04 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,116 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,116 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,116 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,116 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,119 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-05 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 9.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,120 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,120 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,120 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,120 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,121 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-06 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,123 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,123 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,124 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,124 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,125 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-07 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 10.0, 'SOL/USDT': 11.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 0.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 8.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:56,127 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,127 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,127 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,127 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,129 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-08 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 6.0, 'SOL/USDT': 9.0, 'SUI/USDT': 13.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 5.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:56,131 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,131 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,131 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,132 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,133 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-09 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 4.0, 'SOL/USDT': 9.0, 'SUI/USDT': 12.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 1.0, 'DOT/USDT': 7.0}
2025-07-16 06:05:56,135 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,136 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,136 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,136 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,136 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-05-10:
2025-07-16 06:05:56,136 - [KRAKEN] - root - INFO -    Signal Date: 2025-05-09 (generated at 00:00 UTC)
2025-07-16 06:05:56,136 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-10 00:00 UTC (immediate)
2025-07-16 06:05:56,136 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:56,136 - [KRAKEN] - root - INFO -    Selling: ['SUI/USDT']
2025-07-16 06:05:56,136 - [KRAKEN] - root - INFO -    Buying: ['PEPE/USDT']
2025-07-16 06:05:56,136 - [KRAKEN] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-16 06:05:56,140 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-10 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 12.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:56,140 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,140 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,140 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,140 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,143 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-11 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 12.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:56,144 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,144 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,144 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,144 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,147 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-12 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 11.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:56,148 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,148 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,148 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,148 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,151 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-13 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 5.0, 'LINK/USDT': 6.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:56,151 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,152 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,152 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,152 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,153 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-14 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 2.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:56,155 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,155 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,155 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,156 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,157 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-15 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:56,159 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,159 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,160 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,160 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,163 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-16 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 3.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:56,163 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,164 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,164 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,164 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,165 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-17 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 4.0, 'SOL/USDT': 7.0, 'SUI/USDT': 10.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:56,167 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,167 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,167 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,168 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,169 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-18 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 4.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:56,171 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,171 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,171 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,172 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,173 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-19 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 5.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:56,175 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,175 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,175 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,175 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,177 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-20 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 8.0}
2025-07-16 06:05:56,179 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,179 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,179 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,180 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,180 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-05-21:
2025-07-16 06:05:56,180 - [KRAKEN] - root - INFO -    Signal Date: 2025-05-20 (generated at 00:00 UTC)
2025-07-16 06:05:56,180 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-21 00:00 UTC (immediate)
2025-07-16 06:05:56,180 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:56,180 - [KRAKEN] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-16 06:05:56,180 - [KRAKEN] - root - INFO -    Buying: ['AAVE/USDT']
2025-07-16 06:05:56,180 - [KRAKEN] - root - INFO -    AAVE/USDT buy price: $247.5400 (close price)
2025-07-16 06:05:56,184 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-21 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 4.0, 'LINK/USDT': 5.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 1.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:56,184 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,184 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,184 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,184 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,184 - [KRAKEN] - root - INFO - [TIE-BREAKING] Incumbent approach: Keeping AAVE/USDT (tied at score 12.0)
2025-07-16 06:05:56,187 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-22 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 9.0, 'XRP/USDT': 1.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 0.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 1.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:56,188 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,188 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,188 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,188 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,188 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-05-23:
2025-07-16 06:05:56,188 - [KRAKEN] - root - INFO -    Signal Date: 2025-05-22 (generated at 00:00 UTC)
2025-07-16 06:05:56,188 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-23 00:00 UTC (immediate)
2025-07-16 06:05:56,188 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:56,188 - [KRAKEN] - root - INFO -    Selling: ['AAVE/USDT']
2025-07-16 06:05:56,188 - [KRAKEN] - root - INFO -    Buying: ['PEPE/USDT']
2025-07-16 06:05:56,188 - [KRAKEN] - root - INFO -    PEPE/USDT buy price: $0.0000 (close price)
2025-07-16 06:05:56,192 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-23 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 9.0, 'SUI/USDT': 7.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 13.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 2.0, 'DOT/USDT': 6.0}
2025-07-16 06:05:56,192 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,192 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,192 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,192 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,196 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-24 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 9.0, 'SUI/USDT': 6.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 12.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 2.0, 'LINK/USDT': 1.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 5.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:56,196 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,196 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,196 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,196 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,196 - [KRAKEN] - root - INFO - [TIE-BREAKING] Incumbent approach: Keeping PEPE/USDT (tied at score 12.0)
2025-07-16 06:05:56,200 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-25 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 3.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 2.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 5.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,200 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,200 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,200 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,200 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,200 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-05-26:
2025-07-16 06:05:56,201 - [KRAKEN] - root - INFO -    Signal Date: 2025-05-25 (generated at 00:00 UTC)
2025-07-16 06:05:56,201 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-05-26 00:00 UTC (immediate)
2025-07-16 06:05:56,201 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:56,201 - [KRAKEN] - root - INFO -    Selling: ['PEPE/USDT']
2025-07-16 06:05:56,201 - [KRAKEN] - root - INFO -    Buying: ['AAVE/USDT']
2025-07-16 06:05:56,201 - [KRAKEN] - root - INFO -    AAVE/USDT buy price: $267.5200 (close price)
2025-07-16 06:05:56,204 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-26 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 4.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 6.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,204 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,205 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,205 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,205 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,208 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-27 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 9.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 7.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,209 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,209 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,209 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,209 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,212 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-28 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 7.0, 'SOL/USDT': 8.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 2.0, 'TRX/USDT': 5.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,213 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,213 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,213 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,213 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,216 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-29 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 6.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 0.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 2.0, 'TRX/USDT': 6.0, 'PEPE/USDT': 12.0, 'DOGE/USDT': 10.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,217 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,217 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,217 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,217 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,220 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-30 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 2.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,220 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,220 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,221 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,221 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,224 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-05-31 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,224 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,224 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,225 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,225 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,228 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-01 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 3.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,228 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,228 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,228 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,229 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,232 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-02 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 4.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,232 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,232 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,232 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,233 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,236 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-03 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 8.0, 'PEPE/USDT': 11.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,238 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,238 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,239 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,239 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,240 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-04 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 1.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,240 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,240 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,240 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,241 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,244 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-05 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 2.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 10.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:56,244 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,244 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,244 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,244 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,248 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-06 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 6.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,248 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,248 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,248 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,248 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,252 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-07 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,252 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,252 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,252 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,252 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,255 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-08 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 9.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,256 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,256 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,256 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,256 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,257 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-09 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 1.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 4.0, 'LINK/USDT': 3.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,259 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,259 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,260 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,260 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,263 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-10 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 5.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,265 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,265 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,265 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,265 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,267 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-11 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 6.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 7.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,267 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,269 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,269 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,269 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,271 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-12 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 4.0, 'SUI/USDT': 1.0, 'XRP/USDT': 7.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 3.0, 'LINK/USDT': 4.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,271 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,271 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,273 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,273 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,275 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-13 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 1.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,275 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,275 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,275 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,275 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,279 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-14 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 5.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,279 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,279 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,281 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,281 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,283 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-15 00:00:00+00:00: {'ETH/USDT': 12.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 5.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 5.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,285 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,285 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,286 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,286 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,289 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-16 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:56,290 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,290 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,290 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,290 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,293 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-17 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:56,293 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,293 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,294 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,294 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,295 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-18 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:56,297 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,297 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,297 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,297 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,299 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-19 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 10.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 9.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:56,301 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,301 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,302 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,302 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,308 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-20 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 10.0, 'DOT/USDT': 3.0}
2025-07-16 06:05:56,308 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,308 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,308 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,308 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,308 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-06-21:
2025-07-16 06:05:56,308 - [KRAKEN] - root - INFO -    Signal Date: 2025-06-20 (generated at 00:00 UTC)
2025-07-16 06:05:56,308 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-06-21 00:00 UTC (immediate)
2025-07-16 06:05:56,308 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:56,309 - [KRAKEN] - root - INFO -    Selling: ['AAVE/USDT']
2025-07-16 06:05:56,310 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-21 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 8.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 0.0, 'BNB/USDT': 11.0, 'DOT/USDT': 4.0}
2025-07-16 06:05:56,310 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,310 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,310 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,310 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,314 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-22 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 0.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 2.0, 'BNB/USDT': 11.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,314 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,314 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,314 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,314 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,320 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-23 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,320 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,320 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,320 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,320 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,324 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-24 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 4.0, 'LINK/USDT': 6.0, 'TRX/USDT': 13.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 1.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,324 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,324 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,324 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,324 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,327 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-25 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 12.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,328 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,328 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,328 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,328 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,331 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-26 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 3.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,332 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,332 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,332 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,332 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,335 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-27 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 13.0, 'SOL/USDT': 6.0, 'SUI/USDT': 2.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 7.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,336 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,336 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,336 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,336 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,337 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-28 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 13.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 9.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 2.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 11.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,339 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,339 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,339 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,340 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,341 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-29 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 7.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 10.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,343 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,343 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,343 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,343 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,347 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-06-30 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 12.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,348 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,348 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,348 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,348 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,351 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-01 00:00:00+00:00: {'ETH/USDT': 6.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,352 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,352 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,352 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,352 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,356 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-02 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 8.0, 'SUI/USDT': 3.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 4.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,356 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,356 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,356 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,356 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,358 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-03 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 0.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,359 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,360 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,360 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,360 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,361 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-04 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,363 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,363 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,363 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,363 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,367 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-05 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 3.0, 'BNB/USDT': 8.0, 'DOT/USDT': 1.0}
2025-07-16 06:05:56,368 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,368 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,368 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,368 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,369 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-06 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 11.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:56,369 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,369 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,370 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,370 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,373 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-07 00:00:00+00:00: {'ETH/USDT': 7.0, 'BTC/USDT': 10.0, 'SOL/USDT': 7.0, 'SUI/USDT': 5.0, 'XRP/USDT': 9.0, 'AAVE/USDT': 11.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 6.0, 'TRX/USDT': 12.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 8.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:56,374 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,374 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,374 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,374 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,378 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-08 00:00:00+00:00: {'ETH/USDT': 8.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 11.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 11.0, 'PEPE/USDT': 0.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 6.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:56,378 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,378 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,378 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,378 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,382 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-09 00:00:00+00:00: {'ETH/USDT': 11.0, 'BTC/USDT': 8.0, 'SOL/USDT': 6.0, 'SUI/USDT': 5.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 13.0, 'AVAX/USDT': 2.0, 'ADA/USDT': 1.0, 'LINK/USDT': 7.0, 'TRX/USDT': 9.0, 'PEPE/USDT': 1.0, 'DOGE/USDT': 4.0, 'BNB/USDT': 4.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:56,384 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,384 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,385 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,385 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,389 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-10 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 4.0, 'SUI/USDT': 11.0, 'XRP/USDT': 10.0, 'AAVE/USDT': 10.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 3.0, 'LINK/USDT': 8.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 6.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:56,389 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,389 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,389 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,389 - [KRAKEN] - root - INFO - [DEBUG] NOT applying incumbent tie-breaking: strategy=incumbent, current_holdings=False, filtered_scores=True
2025-07-16 06:05:56,390 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-07-11:
2025-07-16 06:05:56,390 - [KRAKEN] - root - INFO -    Signal Date: 2025-07-10 (generated at 00:00 UTC)
2025-07-16 06:05:56,390 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-11 00:00 UTC (immediate)
2025-07-16 06:05:56,390 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:56,390 - [KRAKEN] - root - INFO -    Buying: ['SUI/USDT']
2025-07-16 06:05:56,392 - [KRAKEN] - root - INFO -    SUI/USDT buy price: $3.3868 (close price)
2025-07-16 06:05:56,397 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-11 00:00:00+00:00: {'ETH/USDT': 10.0, 'BTC/USDT': 3.0, 'SOL/USDT': 3.0, 'SUI/USDT': 10.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 1.0, 'ADA/USDT': 8.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 9.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:56,397 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,397 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,397 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,397 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,398 - [KRAKEN] - root - INFO - ASSET CHANGE DETECTED on 2025-07-12:
2025-07-16 06:05:56,398 - [KRAKEN] - root - INFO -    Signal Date: 2025-07-11 (generated at 00:00 UTC)
2025-07-16 06:05:56,398 - [KRAKEN] - root - INFO -    AUTOMATIC EXECUTION: 2025-07-12 00:00 UTC (immediate)
2025-07-16 06:05:56,398 - [KRAKEN] - root - INFO -    Execution Delay: 0 hours
2025-07-16 06:05:56,400 - [KRAKEN] - root - INFO -    Selling: ['SUI/USDT']
2025-07-16 06:05:56,400 - [KRAKEN] - root - INFO -    Buying: ['XRP/USDT']
2025-07-16 06:05:56,401 - [KRAKEN] - root - INFO -    XRP/USDT buy price: $2.7395 (close price)
2025-07-16 06:05:56,406 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-12 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 3.0, 'SUI/USDT': 10.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 9.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 10.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 0.0, 'DOT/USDT': 0.0}
2025-07-16 06:05:56,406 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,406 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,406 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,406 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,411 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-13 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 9.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 8.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,411 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,413 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,413 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,414 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,418 - [KRAKEN] - root - ERROR - 🚨 [RAW SCORES] daily_scores_df at 2025-07-14 00:00:00+00:00: {'ETH/USDT': 9.0, 'BTC/USDT': 4.0, 'SOL/USDT': 2.0, 'SUI/USDT': 12.0, 'XRP/USDT': 13.0, 'AAVE/USDT': 5.0, 'AVAX/USDT': 3.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 7.0, 'BNB/USDT': 0.0, 'DOT/USDT': 2.0}
2025-07-16 06:05:56,418 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Asset selection path: use_weighted_allocation=False
2025-07-16 06:05:56,418 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Using equal allocation path
2025-07-16 06:05:56,418 - [KRAKEN] - root - ERROR - 🚨 [MAIN] Checking momentum condition: tie_breaking_strategy=incumbent, previous_scores=False
2025-07-16 06:05:56,419 - [KRAKEN] - root - INFO - [DEBUG] Applying incumbent tie-breaking: strategy=incumbent
2025-07-16 06:05:56,599 - [KRAKEN] - root - INFO - Entry trade at 2025-04-23 00:00:00+00:00: SOL/USDT
2025-07-16 06:05:56,599 - [KRAKEN] - root - INFO - Swap trade at 2025-04-24 00:00:00+00:00: SOL/USDT -> SUI/USDT
2025-07-16 06:05:56,601 - [KRAKEN] - root - INFO - Swap trade at 2025-05-10 00:00:00+00:00: SUI/USDT -> PEPE/USDT
2025-07-16 06:05:56,602 - [KRAKEN] - root - INFO - Swap trade at 2025-05-21 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-16 06:05:56,602 - [KRAKEN] - root - INFO - Swap trade at 2025-05-23 00:00:00+00:00: AAVE/USDT -> PEPE/USDT
2025-07-16 06:05:56,602 - [KRAKEN] - root - INFO - Swap trade at 2025-05-26 00:00:00+00:00: PEPE/USDT -> AAVE/USDT
2025-07-16 06:05:56,603 - [KRAKEN] - root - INFO - Exit trade at 2025-06-21 00:00:00+00:00 from AAVE/USDT
2025-07-16 06:05:56,603 - [KRAKEN] - root - INFO - Entry trade at 2025-07-11 00:00:00+00:00: SUI/USDT
2025-07-16 06:05:56,603 - [KRAKEN] - root - INFO - Swap trade at 2025-07-12 00:00:00+00:00: SUI/USDT -> XRP/USDT
2025-07-16 06:05:56,603 - [KRAKEN] - root - INFO - Total trades: 9 (Entries: 2, Exits: 1, Swaps: 6)
2025-07-16 06:05:56,611 - [KRAKEN] - root - INFO - Strategy execution completed in 0s
2025-07-16 06:05:56,611 - [KRAKEN] - root - INFO - DEBUG: self.elapsed_time = 0.8957288265228271 seconds
2025-07-16 06:05:56,644 - [KRAKEN] - root - INFO - Saved allocation history to allocation_history_1d_1d_with_mtpi_no_rebal_independent_incumbent_2025-02-10.csv
2025-07-16 06:05:56,646 - [KRAKEN] - root - INFO - Strategy equity curve starts at: 2025-02-10
2025-07-16 06:05:56,647 - [KRAKEN] - root - INFO - Assets included in buy-and-hold comparison:
2025-07-16 06:05:56,647 - [KRAKEN] - root - INFO -   ETH/USDT: First available date 2024-12-12
2025-07-16 06:05:56,647 - [KRAKEN] - root - INFO -   BTC/USDT: First available date 2024-12-12
2025-07-16 06:05:56,647 - [KRAKEN] - root - INFO -   SOL/USDT: First available date 2024-12-12
2025-07-16 06:05:56,647 - [KRAKEN] - root - INFO -   SUI/USDT: First available date 2024-12-12
2025-07-16 06:05:56,647 - [KRAKEN] - root - INFO -   XRP/USDT: First available date 2024-12-12
2025-07-16 06:05:56,647 - [KRAKEN] - root - INFO -   AAVE/USDT: First available date 2024-12-12
2025-07-16 06:05:56,647 - [KRAKEN] - root - INFO -   AVAX/USDT: First available date 2024-12-12
2025-07-16 06:05:56,647 - [KRAKEN] - root - INFO -   ADA/USDT: First available date 2024-12-12
2025-07-16 06:05:56,647 - [KRAKEN] - root - INFO -   LINK/USDT: First available date 2024-12-12
2025-07-16 06:05:56,647 - [KRAKEN] - root - INFO -   TRX/USDT: First available date 2024-12-12
2025-07-16 06:05:56,647 - [KRAKEN] - root - INFO -   PEPE/USDT: First available date 2024-12-12
2025-07-16 06:05:56,647 - [KRAKEN] - root - INFO -   DOGE/USDT: First available date 2024-12-12
2025-07-16 06:05:56,648 - [KRAKEN] - root - INFO -   BNB/USDT: First available date 2024-12-12
2025-07-16 06:05:56,648 - [KRAKEN] - root - INFO -   DOT/USDT: First available date 2024-12-12
2025-07-16 06:05:56,648 - [KRAKEN] - root - INFO - Using provided start date for normalization: 2025-02-10 00:00:00+00:00
2025-07-16 06:05:56,655 - [KRAKEN] - root - INFO - Normalized ETH/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:56,659 - [KRAKEN] - root - INFO - Normalized BTC/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:56,663 - [KRAKEN] - root - INFO - Normalized SOL/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:56,667 - [KRAKEN] - root - INFO - Normalized SUI/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:56,671 - [KRAKEN] - root - INFO - Normalized XRP/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:56,676 - [KRAKEN] - root - INFO - Normalized AAVE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:56,680 - [KRAKEN] - root - INFO - Normalized AVAX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:56,686 - [KRAKEN] - root - INFO - Normalized ADA/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:56,690 - [KRAKEN] - root - INFO - Normalized LINK/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:56,695 - [KRAKEN] - root - INFO - Normalized TRX/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:56,701 - [KRAKEN] - root - INFO - Normalized PEPE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:56,704 - [KRAKEN] - root - INFO - Normalized DOGE/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:56,708 - [KRAKEN] - root - INFO - Normalized BNB/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:56,712 - [KRAKEN] - root - INFO - Normalized DOT/USDT B&H curve to start with 10000 on 2025-02-10 00:00:00+00:00, applying 0.1% fee, resulting in 9990.0 actual investment
2025-07-16 06:05:56,719 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for ETH/USDT with 216 points
2025-07-16 06:05:56,720 - [KRAKEN] - root - INFO - ETH/USDT B&H total return: 17.91%
2025-07-16 06:05:56,728 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for BTC/USDT with 216 points
2025-07-16 06:05:56,730 - [KRAKEN] - root - INFO - BTC/USDT B&H total return: 20.86%
2025-07-16 06:05:56,736 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for SOL/USDT with 216 points
2025-07-16 06:05:56,738 - [KRAKEN] - root - INFO - SOL/USDT B&H total return: -18.20%
2025-07-16 06:05:56,746 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for SUI/USDT with 216 points
2025-07-16 06:05:56,747 - [KRAKEN] - root - INFO - SUI/USDT B&H total return: 27.54%
2025-07-16 06:05:56,754 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for XRP/USDT with 216 points
2025-07-16 06:05:56,755 - [KRAKEN] - root - INFO - XRP/USDT B&H total return: 20.44%
2025-07-16 06:05:56,762 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for AAVE/USDT with 216 points
2025-07-16 06:05:56,763 - [KRAKEN] - root - INFO - AAVE/USDT B&H total return: 29.65%
2025-07-16 06:05:56,771 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for AVAX/USDT with 216 points
2025-07-16 06:05:56,771 - [KRAKEN] - root - INFO - AVAX/USDT B&H total return: -12.31%
2025-07-16 06:05:56,779 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for ADA/USDT with 216 points
2025-07-16 06:05:56,782 - [KRAKEN] - root - INFO - ADA/USDT B&H total return: 4.84%
2025-07-16 06:05:56,795 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for LINK/USDT with 216 points
2025-07-16 06:05:56,799 - [KRAKEN] - root - INFO - LINK/USDT B&H total return: -14.35%
2025-07-16 06:05:56,811 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for TRX/USDT with 216 points
2025-07-16 06:05:56,814 - [KRAKEN] - root - INFO - TRX/USDT B&H total return: 22.06%
2025-07-16 06:05:56,825 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for PEPE/USDT with 216 points
2025-07-16 06:05:56,826 - [KRAKEN] - root - INFO - PEPE/USDT B&H total return: 34.10%
2025-07-16 06:05:56,838 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for DOGE/USDT with 216 points
2025-07-16 06:05:56,839 - [KRAKEN] - root - INFO - DOGE/USDT B&H total return: -22.01%
2025-07-16 06:05:56,850 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for BNB/USDT with 216 points
2025-07-16 06:05:56,851 - [KRAKEN] - root - INFO - BNB/USDT B&H total return: 11.52%
2025-07-16 06:05:56,861 - [KRAKEN] - root - INFO - Added equity_curve to bnh_metrics for DOT/USDT with 216 points
2025-07-16 06:05:56,862 - [KRAKEN] - root - INFO - DOT/USDT B&H total return: -15.73%
2025-07-16 06:05:56,874 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:05:56,922 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:05:56,966 - [KRAKEN] - root - INFO - Using colored segments for single-asset strategy visualization
2025-07-16 06:05:57,531 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:05:57,576 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:06:02,254 - [KRAKEN] - root - INFO - Added ETH/USDT buy-and-hold curve with 216 points
2025-07-16 06:06:02,254 - [KRAKEN] - root - INFO - Added BTC/USDT buy-and-hold curve with 216 points
2025-07-16 06:06:02,254 - [KRAKEN] - root - INFO - Added SOL/USDT buy-and-hold curve with 216 points
2025-07-16 06:06:02,254 - [KRAKEN] - root - INFO - Added SUI/USDT buy-and-hold curve with 216 points
2025-07-16 06:06:02,255 - [KRAKEN] - root - INFO - Added XRP/USDT buy-and-hold curve with 216 points
2025-07-16 06:06:02,255 - [KRAKEN] - root - INFO - Added AAVE/USDT buy-and-hold curve with 216 points
2025-07-16 06:06:02,255 - [KRAKEN] - root - INFO - Added AVAX/USDT buy-and-hold curve with 216 points
2025-07-16 06:06:02,255 - [KRAKEN] - root - INFO - Added ADA/USDT buy-and-hold curve with 216 points
2025-07-16 06:06:02,255 - [KRAKEN] - root - INFO - Added LINK/USDT buy-and-hold curve with 216 points
2025-07-16 06:06:02,255 - [KRAKEN] - root - INFO - Added TRX/USDT buy-and-hold curve with 216 points
2025-07-16 06:06:02,255 - [KRAKEN] - root - INFO - Added PEPE/USDT buy-and-hold curve with 216 points
2025-07-16 06:06:02,255 - [KRAKEN] - root - INFO - Added DOGE/USDT buy-and-hold curve with 216 points
2025-07-16 06:06:02,255 - [KRAKEN] - root - INFO - Added BNB/USDT buy-and-hold curve with 216 points
2025-07-16 06:06:02,255 - [KRAKEN] - root - INFO - Added DOT/USDT buy-and-hold curve with 216 points
2025-07-16 06:06:02,255 - [KRAKEN] - root - INFO - Added 14 buy-and-hold curves to results
2025-07-16 06:06:02,256 - [KRAKEN] - root - INFO -   - ETH/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:06:02,256 - [KRAKEN] - root - INFO -   - BTC/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:06:02,256 - [KRAKEN] - root - INFO -   - SOL/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:06:02,256 - [KRAKEN] - root - INFO -   - SUI/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:06:02,256 - [KRAKEN] - root - INFO -   - XRP/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:06:02,256 - [KRAKEN] - root - INFO -   - AAVE/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:06:02,256 - [KRAKEN] - root - INFO -   - AVAX/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:06:02,256 - [KRAKEN] - root - INFO -   - ADA/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:06:02,257 - [KRAKEN] - root - INFO -   - LINK/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:06:02,257 - [KRAKEN] - root - INFO -   - TRX/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:06:02,257 - [KRAKEN] - root - INFO -   - PEPE/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:06:02,257 - [KRAKEN] - root - INFO -   - DOGE/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:06:02,257 - [KRAKEN] - root - INFO -   - BNB/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:06:02,257 - [KRAKEN] - root - INFO -   - DOT/USDT: 216 points from 2024-12-12 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:06:02,299 - [KRAKEN] - root - INFO - Converting trend detection results from USDT to EUR pairs for trading
2025-07-16 06:06:02,299 - [KRAKEN] - root - INFO - Asset conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-16 06:06:02,303 - [KRAKEN] - root - INFO - Successfully converted best asset series from USDT to EUR pairs
2025-07-16 06:06:02,303 - [KRAKEN] - root - INFO - Loading configuration from config/settings.yaml...
2025-07-16 06:06:02,335 - [KRAKEN] - root - INFO - Configuration loaded successfully.
2025-07-16 06:06:02,336 - [KRAKEN] - root - INFO - Loaded MTPI configuration with 8 enabled indicators: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 06:06:02,336 - [KRAKEN] - root - INFO - Using MTPI indicators from config: ['pgo', 'bollinger_bands', 'dwma_score', 'dema_super_score', 'dpsd_score', 'aad_score', 'dynamic_ema_score', 'quantile_dema_score']
2025-07-16 06:06:02,336 - [KRAKEN] - root - INFO - Combination method: consensus
2025-07-16 06:06:02,336 - [KRAKEN] - root - INFO - Calculated appropriate limit for 1d timeframe: 216 candles (minimum 180.0 candles needed for 60 length indicator)
2025-07-16 06:06:02,336 - [KRAKEN] - root - INFO - Checking cache for 1 symbols (1d)...
2025-07-16 06:06:02,370 - [KRAKEN] - root - INFO - Loaded 2157 rows of BTC/USDT data from cache (last updated: 2025-07-16)
2025-07-16 06:06:02,372 - [KRAKEN] - root - INFO - No incomplete daily candles to filter for current date 2025-07-16
2025-07-16 06:06:02,373 - [KRAKEN] - root - INFO - Loaded 2157 rows of BTC/USDT data from cache (after filtering).
2025-07-16 06:06:02,373 - [KRAKEN] - root - INFO - All 1 symbols loaded from cache with sufficient history. Skipping exchange fetch.
2025-07-16 06:06:02,373 - [KRAKEN] - root - INFO - Fetched BTC data: 2157 candles from 2019-08-20 00:00:00+00:00 to 2025-07-15 00:00:00+00:00
2025-07-16 06:06:02,373 - [KRAKEN] - root - INFO - Generating PGO Score signals with length=35, upper_threshold=1.2, lower_threshold=-0.85
2025-07-16 06:06:03,000 - [KRAKEN] - root - INFO - Generated PGO Score signals: {-1: np.int64(996), 0: np.int64(34), 1: np.int64(1127)}
2025-07-16 06:06:03,001 - [KRAKEN] - root - INFO - PGO signal: 1
2025-07-16 06:06:03,001 - [KRAKEN] - root - INFO - Generating BB Score signals with length=33, multiplier=2.0, long_threshold=70, short_threshold=31.0, use_heikin_ashi=False
2025-07-16 06:06:03,115 - [KRAKEN] - root - INFO - Generated BB Score signals: {-1: np.int64(993), 0: np.int64(33), 1: np.int64(1131)}
2025-07-16 06:06:03,117 - [KRAKEN] - root - INFO - Bollinger Bands signal: 1
2025-07-16 06:06:12,232 - [KRAKEN] - root - INFO - Generated DWMA signals using Weighted SD method
2025-07-16 06:06:12,235 - [KRAKEN] - root - INFO - DWMA Score signal: 1
2025-07-16 06:06:13,379 - [KRAKEN] - root - INFO - Generated DEMA Supertrend signals
2025-07-16 06:06:13,380 - [KRAKEN] - root - INFO - Signal distribution: {-1: np.int64(1268), 0: np.int64(1), 1: np.int64(888)}
2025-07-16 06:06:13,380 - [KRAKEN] - root - INFO - Generated DEMA Super Score signals
2025-07-16 06:06:13,380 - [KRAKEN] - root - INFO - DEMA Super Score signal: 1
2025-07-16 06:06:15,814 - [KRAKEN] - root - INFO - Generated DPSD signals
2025-07-16 06:06:15,815 - [KRAKEN] - root - INFO - Signal distribution: {-1: np.int64(1087), 0: np.int64(87), 1: np.int64(983)}
2025-07-16 06:06:15,815 - [KRAKEN] - root - INFO - Generated DPSD Score signals with pertype=60/45
2025-07-16 06:06:15,815 - [KRAKEN] - root - INFO - DPSD Score signal: 1
2025-07-16 06:06:15,919 - [KRAKEN] - root - INFO - Calculated AAD Score with SMA average, length=22, multiplier=1.3
2025-07-16 06:06:15,919 - [KRAKEN] - root - INFO - Generated AAD Score signals using SMA method
2025-07-16 06:06:15,919 - [KRAKEN] - root - INFO - AAD Score signal: 1
2025-07-16 06:06:16,695 - [KRAKEN] - root - INFO - Calculated Dynamic EMA Score with Weighted SD style
2025-07-16 06:06:16,695 - [KRAKEN] - root - INFO - Generated Dynamic EMA Score signals using Weighted SD method
2025-07-16 06:06:16,695 - [KRAKEN] - root - INFO - Dynamic EMA Score signal: 1
2025-07-16 06:06:18,409 - [KRAKEN] - root - INFO - Quantile DEMA Score signal: 1
2025-07-16 06:06:18,410 - [KRAKEN] - root - INFO - Individual signals: {'pgo': 1, 'bollinger_bands': 1, 'dwma_score': 1, 'dema_super_score': 1, 'dpsd_score': 1, 'aad_score': 1, 'dynamic_ema_score': 1, 'quantile_dema_score': 1}
2025-07-16 06:06:18,410 - [KRAKEN] - root - INFO - Combined MTPI signal (consensus): 1
2025-07-16 06:06:18,410 - [KRAKEN] - root - INFO - MTPI Score: 1.000000
2025-07-16 06:06:18,410 - [KRAKEN] - root - INFO - Added current MTPI score to results: 1.000000 (using 1d timeframe)
2025-07-16 06:06:18,414 - [KRAKEN] - root - INFO - Successfully converted assets_held_df from USDT to EUR pairs
2025-07-16 06:06:18,414 - [KRAKEN] - root - INFO - Latest scores extracted (USDT): {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 13.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 06:06:18,414 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Original USDT scores: {'ETH/USDT': 9.0, 'BTC/USDT': 3.0, 'SOL/USDT': 2.0, 'SUI/USDT': 13.0, 'XRP/USDT': 12.0, 'AAVE/USDT': 6.0, 'AVAX/USDT': 6.0, 'ADA/USDT': 10.0, 'LINK/USDT': 7.0, 'TRX/USDT': 1.0, 'PEPE/USDT': 9.0, 'DOGE/USDT': 5.0, 'BNB/USDT': 0.0, 'DOT/USDT': 4.0}
2025-07-16 06:06:18,415 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Original USDT keys order: ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT', 'AAVE/USDT', 'AVAX/USDT', 'ADA/USDT', 'LINK/USDT', 'TRX/USDT', 'PEPE/USDT', 'DOGE/USDT', 'BNB/USDT', 'DOT/USDT']
2025-07-16 06:06:18,415 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Conversion mapping: {'ETH/USDT': 'ETH/EUR', 'BTC/USDT': 'BTC/EUR', 'SOL/USDT': 'SOL/EUR', 'SUI/USDT': 'SUI/EUR', 'XRP/USDT': 'XRP/EUR', 'AAVE/USDT': 'AAVE/EUR', 'AVAX/USDT': 'AVAX/EUR', 'ADA/USDT': 'ADA/EUR', 'LINK/USDT': 'LINK/EUR', 'TRX/USDT': 'TRX/EUR', 'PEPE/USDT': 'PEPE/EUR', 'DOGE/USDT': 'DOGE/EUR', 'BNB/USDT': 'BNB/EUR', 'DOT/USDT': 'DOT/EUR'}
2025-07-16 06:06:18,415 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - ETH/USDT -> ETH/EUR (score: 9.0)
2025-07-16 06:06:18,415 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - BTC/USDT -> BTC/EUR (score: 3.0)
2025-07-16 06:06:18,415 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - SOL/USDT -> SOL/EUR (score: 2.0)
2025-07-16 06:06:18,415 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - SUI/USDT -> SUI/EUR (score: 13.0)
2025-07-16 06:06:18,415 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - XRP/USDT -> XRP/EUR (score: 12.0)
2025-07-16 06:06:18,415 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - AAVE/USDT -> AAVE/EUR (score: 6.0)
2025-07-16 06:06:18,415 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - AVAX/USDT -> AVAX/EUR (score: 6.0)
2025-07-16 06:06:18,415 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - ADA/USDT -> ADA/EUR (score: 10.0)
2025-07-16 06:06:18,415 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - LINK/USDT -> LINK/EUR (score: 7.0)
2025-07-16 06:06:18,415 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - TRX/USDT -> TRX/EUR (score: 1.0)
2025-07-16 06:06:18,415 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - PEPE/USDT -> PEPE/EUR (score: 9.0)
2025-07-16 06:06:18,415 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - DOGE/USDT -> DOGE/EUR (score: 5.0)
2025-07-16 06:06:18,415 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - BNB/USDT -> BNB/EUR (score: 0.0)
2025-07-16 06:06:18,415 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - DOT/USDT -> DOT/EUR (score: 4.0)
2025-07-16 06:06:18,416 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Final EUR scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 06:06:18,416 - [KRAKEN] - root - ERROR - [DEBUG] CONVERSION - Final EUR keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:06:18,416 - [KRAKEN] - root - INFO - Latest scores converted to EUR: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 06:06:18,424 - [KRAKEN] - root - INFO - Saved metrics to new file: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250709_run_20250716_060509.csv
2025-07-16 06:06:18,424 - [KRAKEN] - root - INFO - Saved performance metrics to CSV: Performance_Metrics/metrics_BestAsset_1d_1d_assets14_since_20250709_run_20250716_060509.csv
2025-07-16 06:06:18,424 - [KRAKEN] - root - INFO - === RESULTS STRUCTURE DEBUGGING ===
2025-07-16 06:06:18,424 - [KRAKEN] - root - INFO - Results type: <class 'dict'>
2025-07-16 06:06:18,424 - [KRAKEN] - root - INFO - Results keys: dict_keys(['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message'])
2025-07-16 06:06:18,424 - [KRAKEN] - root - INFO - Success flag set to: True
2025-07-16 06:06:18,424 - [KRAKEN] - root - INFO - Message set to: Strategy calculation completed successfully
2025-07-16 06:06:18,424 - [KRAKEN] - root - INFO -   - strategy_equity: <class 'pandas.core.series.Series'> with 216 entries
2025-07-16 06:06:18,424 - [KRAKEN] - root - INFO -   - buy_hold_curves: dict with 14 entries
2025-07-16 06:06:18,424 - [KRAKEN] - root - INFO -   - best_asset_series: <class 'pandas.core.series.Series'> with 216 entries
2025-07-16 06:06:18,424 - [KRAKEN] - root - INFO -   - mtpi_signals: <class 'pandas.core.series.Series'> with 276 entries
2025-07-16 06:06:18,425 - [KRAKEN] - root - INFO -   - mtpi_score: <class 'float'>
2025-07-16 06:06:18,425 - [KRAKEN] - root - INFO -   - assets_held_df: <class 'pandas.core.frame.DataFrame'> with 216 entries
2025-07-16 06:06:18,425 - [KRAKEN] - root - INFO -   - performance_metrics: dict with 3 entries
2025-07-16 06:06:18,425 - [KRAKEN] - root - INFO -   - metrics_file: <class 'str'>
2025-07-16 06:06:18,425 - [KRAKEN] - root - INFO -   - mtpi_filtering_metadata: dict with 2 entries
2025-07-16 06:06:18,425 - [KRAKEN] - root - INFO -   - success: <class 'bool'>
2025-07-16 06:06:18,425 - [KRAKEN] - root - INFO -   - message: <class 'str'>
2025-07-16 06:06:18,428 - [KRAKEN] - root - ERROR - [DEBUG] STRATEGY RESULTS - Available keys: ['strategy_equity', 'buy_hold_curves', 'best_asset_series', 'mtpi_signals', 'mtpi_score', 'assets_held_df', 'performance_metrics', 'metrics_file', 'mtpi_filtering_metadata', 'success', 'message']
2025-07-16 06:06:18,428 - [KRAKEN] - root - INFO - [DEBUG] ASSET SELECTION - Extracted best_asset from best_asset_series: XRP/EUR
2025-07-16 06:06:18,429 - [KRAKEN] - root - INFO - [DEBUG] ASSET SELECTION - Last 5 entries in best_asset_series: 2025-07-11 00:00:00+00:00    SUI/EUR
2025-07-12 00:00:00+00:00    XRP/EUR
2025-07-13 00:00:00+00:00    XRP/EUR
2025-07-14 00:00:00+00:00    XRP/EUR
2025-07-15 00:00:00+00:00    XRP/EUR
dtype: object
2025-07-16 06:06:18,430 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Latest scores available: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 06:06:18,430 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Scores dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:06:18,430 - [KRAKEN] - root - INFO - [DEBUG] TIE-BREAKING - Strategy: incumbent
2025-07-16 06:06:18,430 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - find_best_asset_for_day() called with 14 assets
2025-07-16 06:06:18,430 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Input scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 06:06:18,430 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Dictionary keys order: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:06:18,430 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Maximum score found: 13.0
2025-07-16 06:06:18,430 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - Assets with max score 13.0: ['SUI/EUR']
2025-07-16 06:06:18,430 - [KRAKEN] - root - WARNING - [DEBUG] ASSET SELECTION - No tie detected, single winner: SUI/EUR
2025-07-16 06:06:18,430 - [KRAKEN] - root - ERROR - [DEBUG] SELECTED BEST ASSET: SUI/EUR (score: 13.0)
2025-07-16 06:06:18,430 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Current day's best asset: SUI/EUR (MTPI signal: 1)
2025-07-16 06:06:18,430 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Maximum score: 13.0
2025-07-16 06:06:18,430 - [KRAKEN] - root - ERROR - [DEBUG] ASSET SELECTION - Assets with max score: ['SUI/EUR']
2025-07-16 06:06:18,430 - [KRAKEN] - root - ERROR - [DEBUG] NO TIE - UPDATING SELECTION: XRP/EUR -> SUI/EUR
2025-07-16 06:06:18,430 - [KRAKEN] - root - ERROR - [DEBUG] NO TIE - Single winner: SUI/EUR
2025-07-16 06:06:18,461 - [KRAKEN] - root - WARNING - No 'assets_held' column found in assets_held_df. Columns: Index(['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR',
       'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR',
       'BNB/EUR', 'DOT/EUR'],
      dtype='object')
2025-07-16 06:06:18,462 - [KRAKEN] - root - INFO - Last row columns: ['ETH/EUR', 'BTC/EUR', 'SOL/EUR', 'SUI/EUR', 'XRP/EUR', 'AAVE/EUR', 'AVAX/EUR', 'ADA/EUR', 'LINK/EUR', 'TRX/EUR', 'PEPE/EUR', 'DOGE/EUR', 'BNB/EUR', 'DOT/EUR']
2025-07-16 06:06:18,462 - [KRAKEN] - root - INFO - Single asset strategy with best asset: SUI/EUR
2025-07-16 06:06:18,462 - [KRAKEN] - root - INFO - [DEBUG] FINAL ASSET SELECTION SUMMARY:
2025-07-16 06:06:18,462 - [KRAKEN] - root - INFO - [DEBUG]   - Best asset selected: SUI/EUR
2025-07-16 06:06:18,462 - [KRAKEN] - root - INFO - [DEBUG]   - Assets held: {'SUI/EUR': 1.0}
2025-07-16 06:06:18,462 - [KRAKEN] - root - INFO - [DEBUG]   - MTPI signal: 1
2025-07-16 06:06:18,462 - [KRAKEN] - root - INFO - [DEBUG]   - Use MTPI signal: True
2025-07-16 06:06:18,462 - [KRAKEN] - root - ERROR - 🚨 ? SUI/EUR WAS SELECTED
2025-07-16 06:06:18,462 - [KRAKEN] - root - INFO - Executing single-asset strategy with best asset: SUI/EUR
2025-07-16 06:06:18,462 - [KRAKEN] - root - INFO - Executing strategy signal: best_asset=SUI/EUR, mtpi_signal=1, mode=live
2025-07-16 06:06:26,512 - [KRAKEN] - root - INFO - Incremented daily trade counter for SUI/EUR: 1/5
2025-07-16 06:06:26,513 - [KRAKEN] - root - INFO - ASSET SELECTION - Attempting to enter position for selected asset: SUI/EUR
2025-07-16 06:06:26,513 - [KRAKEN] - root - INFO - Attempting to enter position for SUI/EUR in live mode
2025-07-16 06:06:26,513 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: Starting enter_position attempt
2025-07-16 06:06:26,513 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: Trading mode: live
2025-07-16 06:06:26,514 - [KRAKEN] - root - INFO - TRADE ATTEMPT - SUI/EUR: Getting current market price...
2025-07-16 06:06:26,514 - [KRAKEN] - root - INFO - [DEBUG] PRICE - SUI/EUR: Starting get_current_price
2025-07-16 06:06:26,514 - [KRAKEN] - root - INFO - [DEBUG] PRICE - SUI/EUR: Exchange ID: kraken
2025-07-16 06:06:26,514 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Initializing exchange kraken
2025-07-16 06:06:26,531 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Exchange initialized successfully
2025-07-16 06:06:26,532 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Exchange markets not loaded, loading now...
2025-07-16 06:06:28,072 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Symbol found after loading markets
2025-07-16 06:06:28,072 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Attempting to fetch ticker...
2025-07-16 06:06:28,567 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker fetched successfully
2025-07-16 06:06:28,567 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker data: {'symbol': 'SUI/EUR', 'timestamp': None, 'datetime': None, 'high': 3.5441, 'low': 3.2907, 'bid': 3.4146, 'bidVolume': 12.0, 'ask': 3.4158, 'askVolume': 12.0, 'vwap': 3.41597879, 'open': 3.5252, 'close': 3.4119, 'last': 3.4119, 'previousClose': None, 'change': -0.1133, 'percentage': -3.2140020424373086, 'average': 3.4685, 'baseVolume': 599105.71192, 'quoteVolume': 2046532.40488657, 'info': {'a': ['3.41580000', '12', '12.000'], 'b': ['3.41460000', '12', '12.000'], 'c': ['3.********', '7.56491'], 'v': ['96588.73050', '599105.71192'], 'p': ['3.47125354', '3.41597879'], 't': ['294', '2918'], 'l': ['3.********', '3.29070000'], 'h': ['3.52690000', '3.54410000'], 'o': '3.52520000'}, 'indexPrice': None, 'markPrice': None}
2025-07-16 06:06:28,568 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Last price: 3.4119
2025-07-16 06:06:28,568 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: get_current_price returned: 3.4119
2025-07-16 06:06:28,568 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price type: <class 'float'>
2025-07-16 06:06:28,568 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - not price: False
2025-07-16 06:06:28,568 - [KRAKEN] - root - INFO - [DEBUG] TRADE - SUI/EUR: Price evaluation - price <= 0: False
2025-07-16 06:06:28,568 - [KRAKEN] - root - INFO - TRADE ATTEMPT - SUI/EUR: Current price: 3.********
2025-07-16 06:06:29,529 - [KRAKEN] - root - INFO - Available balance for EUR: 1.64850000
2025-07-16 06:06:29,529 - [KRAKEN] - root - INFO - Reserved 0.009890 USDC for fees (rate: 0.4% with buffer)
2025-07-16 06:06:29,536 - [KRAKEN] - root - INFO - Loaded market info for 176 trading pairs
2025-07-16 06:06:29,537 - [KRAKEN] - root - WARNING - Order value 1.******** is below minimum order value 5.0 for SUI/EUR
2025-07-16 06:06:29,537 - [KRAKEN] - root - INFO - Using minimum amount for cost 1.******** for SUI/EUR
2025-07-16 06:06:29,537 - [KRAKEN] - root - INFO - Calculated position size for SUI/EUR: 1.******** (using 99.99% of 1.6485, accounting for fees)
2025-07-16 06:06:29,537 - [KRAKEN] - root - INFO - Calculated position size: 1.******** SUI
2025-07-16 06:06:29,537 - [KRAKEN] - root - INFO - Entering position for SUI/EUR: 1.******** units at 3.******** (value: 5.******** EUR)
2025-07-16 06:06:29,537 - [KRAKEN] - root - INFO - Executing live market buy order for SUI/EUR
2025-07-16 06:06:29,597 - [KRAKEN] - root - INFO - Adjusted base amount for SUI/EUR: 1.******** -> 1.********
2025-07-16 06:06:29,701 - [KRAKEN] - root - ERROR - Error creating market buy order: kraken {"error":["EOrder:Insufficient funds"]}
2025-07-16 06:06:29,702 - [KRAKEN] - root - ERROR - TRADE FAILURE - SUI/EUR: Failed to create order for SUI/EUR
2025-07-16 06:06:29,702 - [KRAKEN] - root - ERROR - Trade failed: BUY SUI/EUR, amount=1.********, price=3.********, reason=Failed to create order for SUI/EUR
2025-07-16 06:06:29,704 - [KRAKEN] - root - ERROR - Trade failed: UNKNOWN SUI/EUR, amount=0.********, price=0.********, reason=Failed to create order for SUI/EUR
2025-07-16 06:06:29,704 - [KRAKEN] - root - ERROR - TRADE FAILURE - SUI/EUR: Failed to enter position
2025-07-16 06:06:29,705 - [KRAKEN] - root - ERROR - TRADE FAILURE - SUI/EUR: Error type: order_creation_failed
2025-07-16 06:06:29,705 - [KRAKEN] - root - ERROR - TRADE FAILURE - SUI/EUR: Error reason: Failed to create order for SUI/EUR
2025-07-16 06:06:29,705 - [KRAKEN] - root - WARNING - HIGH-PRIORITY ASSET REJECTED: SUI/EUR - Failed to create order for SUI/EUR
2025-07-16 06:06:29,706 - [KRAKEN] - root - ERROR - Trade failed: UNKNOWN SUI/EUR, amount=0.********, price=0.********, reason=Failed to create order for SUI/EUR
2025-07-16 06:06:29,706 - [KRAKEN] - root - INFO - Single-asset trade result logged to trade log file
2025-07-16 06:06:29,707 - [KRAKEN] - root - ERROR - Trade execution failed: {'success': False, 'reason': 'Failed to create order for SUI/EUR', 'symbol': 'SUI/EUR', 'error_type': 'order_creation_failed'}
2025-07-16 06:06:29,767 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 400 Bad Request"
2025-07-16 06:06:29,768 - [KRAKEN] - root - WARNING - Failed to send with Markdown formatting: Can't parse entities: can't find end of the entity starting at byte offset 91
2025-07-16 06:06:29,813 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-07-16 06:06:29,815 - [KRAKEN] - root - INFO - Asset selection logged: 1 assets selected with single_asset allocation
2025-07-16 06:06:29,815 - [KRAKEN] - root - INFO - Asset scores (sorted by score):
2025-07-16 06:06:29,815 - [KRAKEN] - root - INFO -   SUI/EUR: score=13.0, status=SELECTED, weight=1.00
2025-07-16 06:06:29,815 - [KRAKEN] - root - INFO -   XRP/EUR: score=12.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:29,815 - [KRAKEN] - root - INFO -   ADA/EUR: score=10.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:29,815 - [KRAKEN] - root - INFO -   ETH/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:29,815 - [KRAKEN] - root - INFO -   PEPE/EUR: score=9.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:29,816 - [KRAKEN] - root - INFO -   LINK/EUR: score=7.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:29,816 - [KRAKEN] - root - INFO -   AAVE/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:29,816 - [KRAKEN] - root - INFO -   AVAX/EUR: score=6.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:29,816 - [KRAKEN] - root - INFO -   DOGE/EUR: score=5.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:29,816 - [KRAKEN] - root - INFO -   DOT/EUR: score=4.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:29,816 - [KRAKEN] - root - INFO -   BTC/EUR: score=3.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:29,816 - [KRAKEN] - root - INFO -   SOL/EUR: score=2.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:29,816 - [KRAKEN] - root - INFO -   TRX/EUR: score=1.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:29,816 - [KRAKEN] - root - INFO -   BNB/EUR: score=0.0, status=NOT SELECTED, weight=0.00
2025-07-16 06:06:29,816 - [KRAKEN] - root - INFO - Asset selection logged with 14 assets scored and 1 assets selected
2025-07-16 06:06:29,816 - [KRAKEN] - root - INFO - Extracted asset scores: {'ETH/EUR': 9.0, 'BTC/EUR': 3.0, 'SOL/EUR': 2.0, 'SUI/EUR': 13.0, 'XRP/EUR': 12.0, 'AAVE/EUR': 6.0, 'AVAX/EUR': 6.0, 'ADA/EUR': 10.0, 'LINK/EUR': 7.0, 'TRX/EUR': 1.0, 'PEPE/EUR': 9.0, 'DOGE/EUR': 5.0, 'BNB/EUR': 0.0, 'DOT/EUR': 4.0}
2025-07-16 06:06:29,856 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-07-16 06:06:29,858 - [KRAKEN] - root - INFO - Strategy execution completed successfully in 80.67 seconds
2025-07-16 06:06:29,862 - [KRAKEN] - root - INFO - Saved recovery state to data/state/recovery_state.json
2025-07-16 12:00:52,111 - [KRAKEN] - root - INFO - [DEBUG] PRICE - SUI/EUR: Starting get_current_price
2025-07-16 12:00:52,114 - [KRAKEN] - root - INFO - [DEBUG] PRICE - SUI/EUR: Exchange ID: kraken
2025-07-16 12:00:52,115 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Using existing exchange instance
2025-07-16 12:00:52,116 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Symbol found in exchange markets
2025-07-16 12:00:52,116 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Attempting to fetch ticker...
2025-07-16 12:00:52,216 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker fetched successfully
2025-07-16 12:00:52,217 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Ticker data: {'symbol': 'SUI/EUR', 'timestamp': None, 'datetime': None, 'high': 3.5441, 'low': 3.3333, 'bid': 3.4539, 'bidVolume': 3389.0, 'ask': 3.454, 'askVolume': 805.0, 'vwap': 3.44984081, 'open': 3.5252, 'close': 3.454, 'last': 3.454, 'previousClose': None, 'change': -0.0712, 'percentage': -2.019743560649041, 'average': 3.4896, 'baseVolume': 501612.05327, 'quoteVolume': 1730481.73215874, 'info': {'a': ['3.45400000', '805', '805.000'], 'b': ['3.45390000', '3389', '3389.000'], 'c': ['3.45400000', '16.20766'], 'v': ['188484.77120', '501612.05327'], 'p': ['3.46756514', '3.44984081'], 't': ['838', '2575'], 'l': ['3.********', '3.33330000'], 'h': ['3.52690000', '3.54410000'], 'o': '3.52520000'}, 'indexPrice': None, 'markPrice': None}
2025-07-16 12:00:52,217 - [KRAKEN] - root - ERROR - [DEBUG] PRICE - SUI/EUR: Last price: 3.454
2025-07-16 12:00:52,321 - [KRAKEN] - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7774551300:AAFgV381k72Df_ItEB6Qj6WmIupkMXWGWSM/sendMessage "HTTP/1.1 200 OK"
2025-07-16 12:00:52,325 - [KRAKEN] - root - INFO - Status update sent
